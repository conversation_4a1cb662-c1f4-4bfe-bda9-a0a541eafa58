2025-06-17 20:35:57 | INFO     | __main__:<module>:116 - Executing script to create 'bms_charging_data' table...
2025-06-17 20:35:57 | INFO     | code.db_operations.pg_db_utils:get_db_connection:73 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 20:35:57 | INFO     | __main__:create_bms_charging_data_table:100 - Attempting to create table 'bms_charging_data' if it does not exist...
2025-06-17 20:35:57 | INFO     | __main__:create_bms_charging_data_table:103 - Table 'bms_charging_data' checked/created successfully.
2025-06-17 20:35:57 | INFO     | __main__:create_bms_charging_data_table:113 - Database connection closed.
2025-06-17 20:35:57 | INFO     | __main__:<module>:118 - Script execution finished.
2025-06-17 22:58:54 | INFO     | __main__:store_all_csv_to_raw_table:106 - Scanning directory for CSV files to load into raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input
2025-06-17 22:58:54 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ABDCB0BHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 Pro Gen1_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:58:54 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 1411 rows from P53ABDCB0BHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 Pro Gen1_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 190 rows with null 'ts_bms' from P53ABDCB0BHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 Pro Gen1_XX_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:55 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 1221 rows into raw table 'bms_charging_data_raw' from P53ABDCB0BHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 Pro Gen1_XX_LG 4kwh_XX_XX.csv...
2025-06-17 22:58:55 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ABDCB0BHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 Pro Gen1_XX_LG 4kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:58:55 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53ABDCB0BHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 Pro Gen1_XX_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ABDCB5BHA00013_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 3kwh_XX_XX.csv
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 581 rows from P53ABDCB5BHA00013_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 3kwh_XX_XX.csv
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 82 rows with null 'ts_bms' from P53ABDCB5BHA00013_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 3kwh_XX_XX.csv.
2025-06-17 22:58:55 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 499 rows into raw table 'bms_charging_data_raw' from P53ABDCB5BHA00013_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 3kwh_XX_XX.csv...
2025-06-17 22:58:55 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ABDCB5BHA00013_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 3kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:58:55 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53ABDCB5BHA00013_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 3kwh_XX_XX.csv.
2025-06-17 22:58:55 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ACDCB0AKA00015_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro Gen1_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | ERROR    | __main__:store_single_csv_to_raw_table:26 - Error reading CSV file C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ACDCB0AKA00015_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro Gen1_XX_LG 4kwh_XX_XX.csv with Polars: could not parse `001affffffffffff` as dtype `i64` at column 'evse_identification_low_byte' (column number 97)

The current offset in the file is 28556 bytes.

You might want to try:
- increasing `infer_schema_length` (e.g. `infer_schema_length=10000`),
- specifying correct dtype with the `schema_overrides` argument
- setting `ignore_errors` to `True`,
- adding `001affffffffffff` to the `null_values` list.

Original error: ```remaining bytes non-empty```
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53ACDCB2AKA00081_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro Gen1_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 0 rows from P53ACDCB2AKA00081_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro Gen1_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 0 rows with null 'ts_bms' from P53ACDCB2AKA00081_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro Gen1_XX_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:41 - No data left in P53ACDCB2AKA00081_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro Gen1_XX_LG 4kwh_XX_XX.csv after 'ts_bms' null filter. Skipping upload.
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AFDCB2BBA09310_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 0 rows from P53AFDCB2BBA09310_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 0 rows with null 'ts_bms' from P53AFDCB2BBA09310_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:41 - No data left in P53AFDCB2BBA09310_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv after 'ts_bms' null filter. Skipping upload.
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AFDCB7BFA01843_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 497 rows from P53AFDCB7BFA01843_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 88 rows with null 'ts_bms' from P53AFDCB7BFA01843_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:57 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 409 rows into raw table 'bms_charging_data_raw' from P53AFDCB7BFA01843_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv...
2025-06-17 22:58:57 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AFDCB7BFA01843_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:58:57 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53AFDCB7BFA01843_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:57 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AFDCB9BBA09806_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 10362 rows from P53AFDCB9BBA09806_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 764 rows with null 'ts_bms' from P53AFDCB9BBA09806_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:59 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 9598 rows into raw table 'bms_charging_data_raw' from P53AFDCB9BBA09806_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv...
2025-06-17 22:58:59 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AFDCB9BBA09806_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:58:59 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53AFDCB9BBA09806_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 PRO Gen1_Not Confirmed_LG 4kwh_XX_XX.csv.
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCA4CEA00024_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 0 rows from P53AUDCA4CEA00024_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 0 rows with null 'ts_bms' from P53AUDCA4CEA00024_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv.
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:41 - No data left in P53AUDCA4CEA00024_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv after 'ts_bms' null filter. Skipping upload.
2025-06-17 22:58:59 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCA4CGA00402_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 66757 rows from P53AUDCA4CGA00402_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 10468 rows with null 'ts_bms' from P53AUDCA4CGA00402_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:01 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 56289 rows into raw table 'bms_charging_data_raw' from P53AUDCA4CGA00402_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv...
2025-06-17 22:59:01 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCA4CGA00402_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:59:01 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53AUDCA4CGA00402_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCA7CGA00278_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro Gen3_Latest_LG 3kwh_XX_XX.csv
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 0 rows from P53AUDCA7CGA00278_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro Gen3_Latest_LG 3kwh_XX_XX.csv
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 0 rows with null 'ts_bms' from P53AUDCA7CGA00278_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro Gen3_Latest_LG 3kwh_XX_XX.csv.
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:41 - No data left in P53AUDCA7CGA00278_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro Gen3_Latest_LG 3kwh_XX_XX.csv after 'ts_bms' null filter. Skipping upload.
2025-06-17 22:59:01 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCA8CEA00091_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:02 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 3108 rows from P53AUDCA8CEA00091_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:02 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 294 rows with null 'ts_bms' from P53AUDCA8CEA00091_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:02 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:59:02 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 2814 rows into raw table 'bms_charging_data_raw' from P53AUDCA8CEA00091_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv...
2025-06-17 22:59:02 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCA8CEA00091_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:59:02 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53AUDCA8CEA00091_2025-06-15_2025-06-16_23_59_59_23_59_59_FF_S1 Air Gen2_Not Confirmed_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:02 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCAXCGA00159_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Air Gen2_Latest_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 1128 rows from P53AUDCAXCGA00159_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Air Gen2_Latest_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 196 rows with null 'ts_bms' from P53AUDCAXCGA00159_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Air Gen2_Latest_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:03 | INFO     | code.db_operations.pg_db_utils:get_db_connection:77 - Successfully connected to PostgreSQL database 'postgres' on localhost:5999
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:78 - Attempting to insert 932 rows into raw table 'bms_charging_data_raw' from P53AUDCAXCGA00159_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Air Gen2_Latest_BAK 3kwh_XX_XX.csv...
2025-06-17 22:59:03 | ERROR    | __main__:store_single_csv_to_raw_table:84 - Error processing or uploading data from C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCAXCGA00159_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Air Gen2_Latest_BAK 3kwh_XX_XX.csv to raw table: relation "bms_charging_data_raw" does not exist
LINE 1: INSERT INTO bms_charging_data_raw (bms_vehicle_identificatio...
                    ^

2025-06-17 22:59:03 | DEBUG    | __main__:store_single_csv_to_raw_table:92 - Database connection closed for raw table processing of P53AUDCAXCGA00159_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Air Gen2_Latest_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCAXCHA01662_2025-06-15_2025-06-16_23_59_59_23_59_59_Manali_S1 Pro Gen3_Latest_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 0 rows from P53AUDCAXCHA01662_2025-06-15_2025-06-16_23_59_59_23_59_59_Manali_S1 Pro Gen3_Latest_BAK 3kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 0 rows with null 'ts_bms' from P53AUDCAXCHA01662_2025-06-15_2025-06-16_23_59_59_23_59_59_Manali_S1 Pro Gen3_Latest_BAK 3kwh_XX_XX.csv.
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:41 - No data left in P53AUDCAXCHA01662_2025-06-15_2025-06-16_23_59_59_23_59_59_Manali_S1 Pro Gen3_Latest_BAK 3kwh_XX_XX.csv after 'ts_bms' null filter. Skipping upload.
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AUDCC2CHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro+ Gen3_XX_BAK 4kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 0 rows from P53AUDCC2CHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro+ Gen3_XX_BAK 4kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 0 rows with null 'ts_bms' from P53AUDCC2CHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro+ Gen3_XX_BAK 4kwh_XX_XX.csv.
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:41 - No data left in P53AUDCC2CHA00002_2025-06-15_2025-06-16_23_59_59_23_59_59_SS Lab_S1 Pro+ Gen3_XX_BAK 4kwh_XX_XX.csv after 'ts_bms' null filter. Skipping upload.
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:18 - Processing CSV file for raw table: C:\Users\<USER>\codes\olae_enterprise_git\rstudio_projects\cloud_data_processor\code\bms_data_processing\..\..\input\P53AWDCC0CEA00047_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro+ Gen3_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:24 - Successfully read 587 rows from P53AWDCC0CEA00047_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro+ Gen3_XX_LG 4kwh_XX_XX.csv
2025-06-17 22:59:03 | INFO     | __main__:store_single_csv_to_raw_table:38 - Removed 129 rows with null 'ts_bms' from P53AWDCC0CEA00047_2025-06-15_2025-06-16_23_59_59_23_59_59_RMZ_S1 pro+ Gen3_XX_LG 4kwh_XX_XX.csv.
