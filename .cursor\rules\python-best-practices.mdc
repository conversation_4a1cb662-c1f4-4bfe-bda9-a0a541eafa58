---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: false
---
# Python UV Best Practices

## Python Typing

### Type Annotations
- Always use type hints for function parameters and return values
- Use appropriate typing imports: `from typing import List, Dict, Optional, Union, Callable, Any, TypeVar, Generic`
- For Python 3.9+, use built-in collection types for annotations: `list[str]` instead of `List[str]`
- Use `Optional[T]` when a value can be `None`
- Use `Union[Type1, Type2]` when a value can be multiple types
- For callbacks, define the function signature with `Callable[[param_types], return_type]`

### Type Variables
- Use `TypeVar` for generic functions and classes
- Name type variables with descriptive uppercase names (e.g., `T`, `K`, `V`, `TKey`, `TValue`)

### Type Comments
- Add type comments for complex expressions: `# type: List[Dict[str, Any]]`
- Use `# type: ignore` sparingly and only when necessary

## Docstrings

### Format
- Use Google-style docstrings
- Include parameter types and return types in docstrings
- Document exceptions raised
- Add examples for complex functions
- Keep docstrings concise yet informative

### Example
```python
def fetch_data(url: str, timeout: Optional[int] = None) -> dict:
    """Fetches data from the given URL.
    
    Args:
        url: The URL to fetch data from.
        timeout: Request timeout in seconds. Default is None.
        
    Returns:
        The parsed JSON response as a dictionary.
        
    Raises:
        RequestError: If the request fails.
        TimeoutError: If the request times out.
        
    Example:
        >>> data = fetch_data("https://api.example.com/data")
        >>> print(data["status"])
    """
    # Implementation
```

