from .pg_db_utils import get_db_connection, logger, BMS_DATA_COLUMNS

def _get_column_definitions_sql(columns):
    """
    Generates SQL column definitions based on the BMS_DATA_COLUMNS list.
    Maps column names to appropriate SQL data types.
    """
    definitions = []
    for col_name in columns:
        if col_name in [
            "bms_vehicle_identification_number", "bms_scooter_state",
            "charge_mode_mbms", "bms_vehicle_software_version",
            "serial_number", "evse_identification_low_byte",
            "evse_identification_high_byte", "discharge_mode"
        ]:
            definitions.append(f"{col_name} VARCHAR(255)")
        elif col_name == "balancing_started_due_deviation_count":
            definitions.append(f"{col_name} INTEGER")
        elif col_name == "generic_bms_data":
            definitions.append(f"{col_name} TEXT")
        elif col_name == "ts_bms":
            definitions.append(f"{col_name} TIMESTAMP WITHOUT TIME ZONE")
        else: # Default to REAL for all other numeric types
            definitions.append(f"{col_name} REAL")
    return ",\n            ".join(definitions)

def _create_table(table_name):
    """
    Generic function to create a BMS data table with 'id SERIAL PRIMARY KEY'
    and columns defined by BMS_DATA_COLUMNS.
    """
    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error(f"Failed to get database connection. Table creation for '{table_name}' aborted.")
            return

        cursor = conn.cursor()
        column_definitions_sql = _get_column_definitions_sql(BMS_DATA_COLUMNS)

        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id SERIAL PRIMARY KEY,
            {column_definitions_sql}
        );
        """

        logger.info(f"Attempting to create table '{table_name}' if it does not exist...")
        cursor.execute(create_table_query)
        conn.commit()
        logger.info(f"Table '{table_name}' checked/created successfully.")

    except Exception as e:
        logger.error(f"Error creating table '{table_name}': {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            if 'cursor' in locals() and cursor: # Ensure cursor exists before closing
                cursor.close()
            conn.close()
            logger.info(f"Database connection closed for '{table_name}' creation.")

def create_bms_charging_data_table():
    """
    Creates the 'bms_charging_data' table.
    """
    _create_table("bms_charging_data")

def create_bms_charging_data_raw_table():
    """
    Creates the 'bms_charging_data_raw' table.
    """
    _create_table("bms_charging_data_raw")

if __name__ == '__main__':
    logger.info("Executing script to create BMS tables...")
    create_bms_charging_data_table()
    create_bms_charging_data_raw_table()
    logger.info("Script execution finished.")