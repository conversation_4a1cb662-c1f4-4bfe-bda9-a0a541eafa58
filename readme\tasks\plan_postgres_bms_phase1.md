# BMS Charging Data Processing

## POSTGRES Setup
- Setup Table For BMS Data: ✅
  - DB Name : `postgres`
  - Password : `postgres`
  - Table Name : `bms_charging_data_raw`
  - Host : `localhost`
  - Port : `5999`
- Update the db connection in (By making entries .env file for better access):
  - `code/db_store.py`
  - `code/db_delete_data.py`

## BMS Charging Data Processing
- DB Management Scripts (Maintain within `code\db_operations\`):
  - Create a table `bms_charging_data` in `postgres` db with following columns:
    - `id` (Primary Key)
    - `bms_vehicle_identification_number` (varchar)
    - `bms_scooter_state` (varchar)
    - `kwhr_charged_discharged_mode` (float)
    - `total_regen_amhr` (float)
    - `amhr_accumulated_current_cycle` (float)
    - `balancing_temp_pdu` (float)
    - `battery_current` (float)
    - `battery_pack_effective_temp` (float)
    - `battery_pack_temp1` (float)
    - `battery_pack_temp2` (float)
    - `battery_pack_temp3` (float)
    - `battery_pack_temp4` (float)
    - `battery_pack_temp5` (float)
    - `battery_pack_temp6` (float)
    - `bms_dcdcoutput_voltage` (float)
    - `bus_voltage` (float)
    - `cell_voltage_min` (float)
    - `cell_voltage_max` (float)
    - `charge_amhr_bms00` (float)
    - `charge_mode_mbms` (varchar)
    - `discharge_amphr_bms00` (float)
    - `measured_cell1_voltage` (float)
    - `measured_cell2_voltage` (float)
    - `measured_cell3_voltage` (float)
    - `measured_cell4_voltage` (float)
    - `measured_cell5_voltage` (float)
    - `measured_cell6_voltage` (float)
    - `measured_cell7_voltage` (float)
    - `measured_cell8_voltage` (float)
    - `measured_cell9_voltage` (float)
    - `measured_cell10_voltage` (float)
    - `measured_cell11_voltage` (float)
    - `measured_cell12_voltage` (float)
    - `measured_cell13_voltage` (float)
    - `measured_cell14_voltage` (float)
    - `balancing_started_due_deviation_count` (integer)
    - `pack_voltage` (float)
    - `pdu_temp1` (float)
    - `pdu_temp_afe` (float)
    - `pack_soc` (float)
    - `pack_soh` (float)
    - `time_charge80_fc` (float)
    - `time_charge_optimum_fc` (float)
    - `time_charge80_sc` (float)
    - `time_charge_full_sc` (float)
    - `total_balancing_duration` (float)
    - `total_charge_time` (float)
    - `generic_bms_data` (varchar)
    - `balancing_temp` (float)
    - `overall_charge_voltage_limit` (float)
    - `display_soc` (float)
    - `bms00_cell_balancing_temperature` (float)
    - `charge_current_limit00` (float)
    - `discharge_current_limit00` (float)
    - `charge_voltage_limit00` (float)
    - `bms00_pdu_delta_temperature` (float)
    - `bms_vehicle_software_version` (varchar)
    - `overall_discharge_current_limit` (float)
    - `overall_charge_current_limit` (float)
    - `charging_voltage_available_range` (float)
    - `charging_current_availablerange` (float)
    - `charging_rate` (float)
    - `soc_val_from_min_ocv` (float)
    - `soc_debug1` (float)
    - `factored_discharge_amphr` (float)
    - `factored_charge_amphr` (float)
    - `serial_number` (varchar)
    - `evse_identification_low_byte` (varchar)
    - `evse_identification_high_byte` (varchar)
    - `discharge_mode` (varchar)
    - `ts_bms` (timestamp)
  - Then we must proceed with the bms related scripts `code\bms_data_processing\` which will be added to `./run_all.py` after tests:
    1. Store raw script will consider only the bms columns mentioned by dropping other columns and rows where the timestamp is null.
    2. Then comes `code\bms_data_processing\charging_data_processing.py` which will process the raw data and store it in `bms_charging_data` table (Phase 2)

## Requirements from Phase 1:
- DB Management Scripts which will also include create.
- CSV Processing

## Explicit Libraries:
- loguru
- polars instead of pandas