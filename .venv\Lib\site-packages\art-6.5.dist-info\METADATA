Metadata-Version: 2.4
Name: art
Version: 6.5
Summary: ASCII Art Library For Python
Home-page: https://github.com/sepandhaghighi/art
Author: <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: MIT
Project-URL: Webpage, https://www.ascii-art.site
Project-URL: Source, https://github.com/sepandhaghighi/art
Project-URL: Tracker, https://github.com/sepandhaghighi/art/issues
Project-URL: Discord, https://discord.com/invite/FAAyq3QJqP
Keywords: ascii art python3 python text font non-ascii printing
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Science/Research
Classifier: Natural Language :: English
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Text Processing :: Fonts
Classifier: Topic :: Text Editors
Classifier: Topic :: Text Processing :: General
Classifier: Topic :: Utilities
Classifier: Topic :: Multimedia
Classifier: Topic :: Printing
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: AUTHORS.md
Provides-Extra: dev
Requires-Dist: coverage>=4.1; extra == "dev"
Requires-Dist: vulture>=1.0; extra == "dev"
Requires-Dist: bandit>=1.5.1; extra == "dev"
Requires-Dist: pydocstyle>=3.0.0; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-python
Dynamic: summary


<div align="center">
	<img src="https://github.com/sepandhaghighi/art/raw/master/otherfile/logo.png" alt="Logo">
	<br/>
	<br/>
	<a href="https://codecov.io/gh/sepandhaghighi/art"><img src="https://codecov.io/gh/sepandhaghighi/art/branch/master/graph/badge.svg" alt="Codecov"></a>
	<a href="https://badge.fury.io/py/art"><img src="https://badge.fury.io/py/art.svg" alt="PyPI"></a>
	<a href="https://www.python.org/"><img src="https://img.shields.io/badge/built%20with-Python3-green.svg" alt="built with Python3"></a>
	<a href="https://github.com/sepandhaghighi/art"><img alt="GitHub repo size" src="https://img.shields.io/github/repo-size/sepandhaghighi/art"></a>
	<a href="https://github.com/sepandhaghighi/art/blob/master/FontList.ipynb"><img src="https://img.shields.io/badge/Font List-677-blue.svg" alt="Font List"></a>
	<a href="https://github.com/sepandhaghighi/art/blob/master/ArtList.ipynb"><img src="https://img.shields.io/badge/Art List-711-orange.svg" alt="Art List"></a>
	<a href="https://github.com/sepandhaghighi/art/blob/master/DecorList.ipynb"><img src="https://img.shields.io/badge/Decor List-218-green.svg" alt="Decor List"></a>
	<a href="https://t.me/artlib_bot" target="__blank"><img src="https://img.shields.io/badge/Telegram-Bot-red.svg" alt="Telegram Bot"></a>
	<a href="https://anaconda.org/sepandhaghighi/art"><img src="https://anaconda.org/sepandhaghighi/art/badges/version.svg" alt="Anaconda"></a>
	<a href="https://discord.com/invite/FAAyq3QJqP"><img src="https://img.shields.io/discord/897532566301724722.svg" alt="Discord Channel"></a>
</div>
	
----------

## Overview	
ASCII art is also known as "computer text art". It involves the smart placement of typed special characters or
letters to make a visual shape that is spread over multiple lines of text.
	
ART is a Python lib for text converting to ASCII art fancy. ;-)



<table>
	<tr align="center"> 
		<td>Open Hub</td>
		<td><a href="https://www.openhub.net/p/artlib"><img src="https://www.openhub.net/p/artlib/widgets/project_thin_badge.gif"></a></td>	
	</tr>
	<tr align="center">
		<td>PyPI Counter</td>
		<td><a href="https://pepy.tech/projects/art"><img src="https://static.pepy.tech/badge/art" alt="PyPI Downloads"></a></td>
	</tr>
	<tr align="center">
		<td>Github Stars</td>
		<td><a href="https://github.com/sepandhaghighi/art"><img src="https://img.shields.io/github/stars/sepandhaghighi/art.svg?style=social&label=Stars"></a></td>
	</tr>
	<tr align="center">
		<td>Font Counter</td>
		<td id="font_counter">677</td>
	</tr>
	<tr align="center">
		<td>1-Line-Art Counter</td>
		<td id="art_counter">711</td>
	</tr>
	<tr align="center">
		<td>Decor Counter</td>
		<td id="decor_counter">218</td>
	</tr>
</table>



<table>
	<tr> 
		<td align="center">Branch</td>
		<td align="center">master</td>	
		<td align="center">dev</td>	
	</tr>
	<tr>
		<td align="center">CI</td>
		<td align="center"><img src="https://github.com/sepandhaghighi/art/actions/workflows/test.yml/badge.svg?branch=master"></td>
		<td align="center"><img src="https://github.com/sepandhaghighi/art/actions/workflows/test.yml/badge.svg?branch=dev"></td>
	</tr>
</table>

<table>
	<tr> 
		<td align="center">Code Quality</td>
		<td align="center"><a class="badge-align" href="https://www.codacy.com/app/sepand-haghighi/art?utm_source=github.com&amp;utm_medium=referral&amp;utm_content=sepandhaghighi/art&amp;utm_campaign=Badge_Grade"><img src="https://api.codacy.com/project/badge/Grade/405020450bc94088ad1450461831a587"/></a></td>	
		<td align="center"><a href="https://codebeat.co/projects/github-com-sepandhaghighi-art-dev"><img alt="codebeat badge" src="https://codebeat.co/badges/90e77325-a046-4cc5-9c3e-646c011a5b72" /></a></td>	
		<td align="center"><a href="https://www.codefactor.io/repository/github/sepandhaghighi/art"><img src="https://www.codefactor.io/repository/github/sepandhaghighi/art/badge" alt="CodeFactor" /></a></td>
	</tr>
</table>

    

## Usage

<div align="center">

<img src="https://github.com/sepandhaghighi/art/raw/master/otherfile/ART.gif">
<p>Quick Start</p>

</div>				


### 1-Line art

⚠️ Some environments don't support all 1-Line arts

⚠️ **ART 4.6** is the last version to support **Bipartite art**


#### 1. art					

This function return 1-line art as `str` in normal mode and raise `artError` in exception.	
```pycon
>>> from art import *
>>> art_1=art("coffee") # return art as str in normal mode
>>> print(art_1)
c[_]
>>> art_2=art("woman",number=2) # return multiple art as str
>>> print(art_2)
▓⚗_⚗▓ ▓⚗_⚗▓ 
>>> art("coffee", number=3, space=5) 
'c[_]     c[_]     c[_]'
>>> art("random") # random 1-line art mode
'(っ◕‿◕)っ '
>>> art("rand")   # random 1-line art mode
't(-_-t) '
>>> art(22,number=1) # raise artError
Traceback (most recent call last):
        ...
art.art.artError: The 'artname' type must be str.

```
#### 2. aprint				

This function print 1-line art in normal mode (return None) and raise `artError` in exception.
```pycon
>>> aprint("butterfly") # print art
Ƹ̵̡Ӝ̵̨̄Ʒ 
>>> aprint("happy") # print art
 ۜ\(סּںסּَ` )/ۜ
>>> aprint("coffee", number=3, space=5) 
c[_]     c[_]     c[_] 
>>> aprint("random") # random 1-line art mode
'(っ◕‿◕)っ '
>>> aprint("rand")   # random 1-line art mode
't(-_-t) '
>>> aprint("woman",number="22") # raise artError
Traceback (most recent call last):
        ...
art.art.artError: The 'number' type must be int.
```

#### 3. randart

`randart` function is added in `Version 2.2` as `art("random")` shortcut.
```pycon
>>> randart()
'ዞᏜ℘℘Ꮍ ℬℹℛʈዞᗬᏜᎽ '
>>> randart()
'✌(◕‿-)✌ '
```	

ℹ️ Use `ART_NAMES` to access all arts name list (new in `Version 4.2`)

ℹ️ Use `NON_ASCII_ARTS` to access all Non-ASCII arts name list (new in `Version 4.6`)

ℹ️ Use `ASCII_ARTS` to access all ASCII arts name list (new in `Version 5.7`)

### ASCII text
	
⚠️ Some fonts don't support all characters		

⚠️ From `Version 3.3` Non-ASCII fonts added (These fonts are not compatible with some environments)

⚠️ From `Version 5.3` `\n` is used as the default line separator instead of `\r\n` (Use `sep` parameter if needed)

#### 1. text2art				

This function return ASCII text as `str` in normal mode and raise `artError` in exception.
```pycon	
>>> Art=text2art("art") # Return ASCII text (default font) and default chr_ignore=True 
>>> print(Art)
              _   
  __ _  _ __ | |_ 
 / _` || '__|| __|
| (_| || |   | |_ 
 \__,_||_|    \__|
                  
                     
>>> Art=text2art("art",font='block',chr_ignore=True) # Return ASCII text with block font
>>> print(Art)


 .----------------.  .----------------.  .----------------.
| .--------------. || .--------------. || .--------------. |
| |      __      | || |  _______     | || |  _________   | |
| |     /  \     | || | |_   __ \    | || | |  _   _  |  | |
| |    / /\ \    | || |   | |__) |   | || | |_/ | | \_|  | |
| |   / ____ \   | || |   |  __ /    | || |     | |      | |
| | _/ /    \ \_ | || |  _| |  \ \_  | || |    _| |_     | |
| ||____|  |____|| || | |____| |___| | || |   |_____|    | |
| |              | || |              | || |              | |
| '--------------' || '--------------' || '--------------' |
 '----------------'  '----------------'  '----------------'

>>> Art=text2art("test","random") # random font mode
>>> print(Art)
 |       | 
~|~/~/(~~|~
 | \/__) | 
           
>>> Art=text2art("test","rand") # random font mode
>>> print(Art)
___ ____ ____ ___ 
 |  |___ [__   |  
 |  |___ ___]  | 

>>> print(text2art("test", space=10))
 _                                             _   
| |_             ___            ___           | |_ 
| __|           / _ \          / __|          | __|
| |_           |  __/          \__ \          | |_ 
 \__|           \___|          |___/           \__|
                                                   
>>> print(text2art('''Lorem  
ipsum 
dolor''', font="small")) # Multi-line print
 _                            
| |    ___  _ _  ___  _ __    
| |__ / _ \| '_|/ -_)| '  \   
|____|\___/|_|  \___||_|_|_|  
                              
 _                         
(_) _ __  ___ _  _  _ __   
| || '_ \(_-<| || || '  \  
|_|| .__//__/ \_,_||_|_|_| 
   |_|                     
    _       _           
 __| | ___ | | ___  _ _ 
/ _` |/ _ \| |/ _ \| '_|
\__,_|\___/|_|\___/|_|  
                        

>>> print(text2art("test","white_bubble"))  # Non-ASCII font example
ⓣⓔⓢⓣ
>>> text2art("art",font="fancy5",decoration="barcode1") # decoration parameter is added in Version 4.6
'▌│█║▌║▌║ ᏗᏒᏖ ║▌║▌║█│▌'
>>> text2art("seسسس",font=DEFAULT_FONT,chr_ignore=False) # raise artError in exception
Traceback (most recent call last):
        ...
art.art.artError: س is invalid.
  
```
#### 2. tprint				

This function print ASCII text in normal mode (return None) and raise `artError` in exception.
```pycon
>>> tprint("art") # print ASCII text (default font) 
              _   
  __ _  _ __ | |_ 
 / _` || '__|| __|
| (_| || |   | |_ 
 \__,_||_|    \__|
                  

>>> tprint("art",font="block",chr_ignore=True) # print ASCII text (block font)

 .----------------.  .----------------.  .----------------.
| .--------------. || .--------------. || .--------------. |
| |      __      | || |  _______     | || |  _________   | |
| |     /  \     | || | |_   __ \    | || | |  _   _  |  | |
| |    / /\ \    | || |   | |__) |   | || | |_/ | | \_|  | |
| |   / ____ \   | || |   |  __ /    | || |     | |      | |
| | _/ /    \ \_ | || |  _| |  \ \_  | || |    _| |_     | |
| ||____|  |____|| || | |____| |___| | || |   |_____|    | |
| |              | || |              | || |              | |
| '--------------' || '--------------' || '--------------' |
 '----------------'  '----------------'  '----------------'

>>> tprint('testسس')  # chr_ignore flag ==True (Default)
 _               _   
| |_   ___  ___ | |_ 
| __| / _ \/ __|| __|
| |_ |  __/\__ \| |_ 
 \__| \___||___/ \__|
                     
>>> tprint("test","random") # random font mode
 |       | 
~|~/~/(~~|~
 | \/__) | 
           
>>> tprint("test","rand") # random font mode
___ ____ ____ ___ 
 |  |___ [__   |  
 |  |___ ___]  |  
                  
>>> tprint("test", space=10)
 _                                             _   
| |_             ___            ___           | |_ 
| __|           / _ \          / __|          | __|
| |_           |  __/          \__ \          | |_ 
 \__|           \___|          |___/           \__|
                                                   
>>> tprint('testسس',chr_ignore=False) # raise artError in exception 
Traceback (most recent call last):
       ...
art.art.artError: س is invalid.
>>> tprint('''Lorem  
ipsum 
dolor''', font="cybermedium") # Multi-line print
_    ____ ____ ____ _  _    
|    |  | |__/ |___ |\/|    
|___ |__| |  \ |___ |  |    
                            
_ ___  ____ _  _ _  _    
| |__] [__  |  | |\/|    
| |    ___] |__| |  |    
                         
___  ____ _    ____ ____ 
|  \ |  | |    |  | |__/ 
|__/ |__| |___ |__| |  \ 

>>> tprint("art",font="fancy5",decoration="barcode1") # decoration parameter is added in Version 4.6
▌│█║▌║▌║ ᏗᏒᏖ ║▌║▌║█│▌
>>> tprint("art",font="fancy5",decoration="random") # decoration random mode is added in Version 5.0
•]•·✦º✦·»ᏗᏒᏖ«·✦º✦·•[•
```
#### 3. tsave				

This function return `dict` in normal and exception mode.
```pycon
>>> Response=tsave("art",filename="test.txt") # save ASCII text in test.txt file with save message (print_status==True), return dict
Saved! 
Filename: test.txt
>>> Response["Message"]
'OK'
>>> Response=tsave("art",filename="test.txt",print_status=False) # save ASCII text in test.txt file without save message (print_status==False)
>>> Response["Message"]
'OK'
>>> Response["Status"]
True
>>> tsave(22,font=DEFAULT_FONT,filename="art",chr_ignore=True,print_status=True)
{'Status': False, 'Message': "'int' object has no attribute 'split'"}
>>> Response=tsave("art",filename="test.txt",overwrite=True) # overwrite parameter is added in Version 4.0
Saved! 
Filename: test.txt
>>> Response=tsave("art",filename="test.txt",decoration="barcode1") # decoration parameter is added in Version 4.6
Saved! 
Filename: test.txt
>>> Response=tsave("art",filename="test.txt",sep="\r\n") # sep parameter is added in Version 5.3
Saved! 
Filename: test.txt                        
>>> Response=tsave("art",filename="test.txt",space=5) # space parameter is added in Version 6.0
Saved! 
Filename: test.txt                        
```

ℹ️ Use `FONT_NAMES` to access all fonts name list (new in `Version 4.2`)

ℹ️ Use `NON_ASCII_FONTS` to access all Non-ASCII fonts name list (new in `Version 4.4`)

ℹ️ Use `ASCII_FONTS` to access all ASCII fonts name list (new in `Version 5.7`)

### Line

#### 1. lprint

This function prints a grid (`length` by `height`) of any given character in normal mode and raise `artError` in exception.
```pycon
>>> lprint(length=15, height=2, char="*")
***************
***************
```

ℹ️ New in `Version 6.4`

ℹ️ The default values are `length=15`, `height=1`, `char='#'`

#### 2. line

This function returns a grid (`length` by `height`) of any given character as `str` in normal mode and raise `artError` in exception.
```pycon	
>>> line(length=15, height=2, char="*")
'***************\n***************'
```

ℹ️ New in `Version 6.4`

ℹ️ The default values are `length=15`, `height=1`, `char='#'`


### Decoration

⚠️ Some environments don't support all decorations

#### 1. decor

This function return decoration as `str` in normal mode and raise `artError` in exception.
```pycon
>>> decor("barcode1")
'▌│█║▌║▌║ '
>>> decor("barcode1",reverse=True)
' ║▌║▌║█│▌'
>>> decor("barcode1") + text2art("    art   ",font="fancy42") + decor("barcode1",reverse=True)
'▌│█║▌║▌║     ąяţ    ║▌║▌║█│▌'
>>> decor("barcode1",both=True) # both parameter is added in Version 5.0
['▌│█║▌║▌║ ', ' ║▌║▌║█│▌']
>>> decor("random",both=True) # random mode is added in Version 5.0
['｢(◔ω◔「)三', '三三三ʅ(；◔౪◔)ʃ']
>>> decor("rand",both=True) # random mode is added in Version 5.0
['‹–…·´`·…–›', '‹–…·´`·…–›']
>>> decor(None)
Traceback (most recent call last):
	...
art.art.artError: The 'decoration' type must be str.
```

ℹ️ Use `DECORATION_NAMES` to access all decorations name list (new in `Version 4.6`)

### Font modes

These modes are available for `text2art`, `tprint` & `tsave`.	

#### 1. Font name	

⚠️ Some fonts don't support all characters
			
```pycon

>>> tprint("art",font="block",chr_ignore=True)

 .----------------.  .----------------.  .----------------.
| .--------------. || .--------------. || .--------------. |
| |      __      | || |  _______     | || |  _________   | |
| |     /  \     | || | |_   __ \    | || | |  _   _  |  | |
| |    / /\ \    | || |   | |__) |   | || | |_/ | | \_|  | |
| |   / ____ \   | || |   |  __ /    | || |     | |      | |
| | _/ /    \ \_ | || |  _| |  \ \_  | || |    _| |_     | |
| ||____|  |____|| || | |____| |___| | || |   |_____|    | |
| |              | || |              | || |              | |
| '--------------' || '--------------' || '--------------' |
 '----------------'  '----------------'  '----------------'

>>> tprint("art","white_bubble")
ⓐⓡⓣ

```	

#### 2. Random		

Randomly select from all fonts.	

Keywords : `random`, `rand` & `rnd`

```pycon
>>> tprint("test",font="random")
 |       | 
~|~/~/(~~|~
 | \/__) | 
  

```

#### 3. Random small

Randomly select from small fonts.	

Keywords : `rnd-small`, `random-small` & `rand-small`

ℹ️ New in `Version 2.8`

```pycon
>>> tprint("test",font="rnd-small")
             
_/  _   _ _/ 
/  (- _)  /  
             
```

#### 4. Random medium

Randomly select from medium fonts.

Keywords : `rnd-medium`, `random-medium` & `rand-medium`

ℹ️ New in `Version 2.8`

```pycon
>>> tprint("test",font="rnd-medium")
                      
  ,                ,  
 ||               ||  
=||=  _-_   _-_, =||= 
 ||  || \\ ||_.   ||  
 ||  ||/    ~ ||  ||  
 \\, \\,/  ,-_-   \\, 
                      
                      
```

#### 5. Random large

Randomly select from large fonts.

Keywords : `rnd-large`, `random-large` & `rand-large`

ℹ️ New in `Version 2.8`

```pycon
>>> tprint("test",font="rnd-large")
                                                                    
8888888 8888888888 8 8888888888      d888888o.   8888888 8888888888 
      8 8888       8 8888          .`8888:' `88.       8 8888       
      8 8888       8 8888          8.`8888.   Y8       8 8888       
      8 8888       8 8888          `8.`8888.           8 8888       
      8 8888       8 888888888888   `8.`8888.          8 8888       
      8 8888       8 8888            `8.`8888.         8 8888       
      8 8888       8 8888             `8.`8888.        8 8888       
      8 8888       8 8888         8b   `8.`8888.       8 8888       
      8 8888       8 8888         `8b.  ;8.`8888       8 8888       
      8 8888       8 888888888888  `Y8888P ,88P'       8 8888       


```

#### 6. Random xlarge

Randomly select from xlarge fonts.	

Keywords : `rnd-xlarge`, `random-xlarge` & `rand-xlarge`

ℹ️ New in `Version 2.8`

```pycon
>>> tprint("test","rnd-xlarge")
      _____                    _____                    _____                _____          
     /\    \                  /\    \                  /\    \              /\    \         
    /::\    \                /::\    \                /::\    \            /::\    \        
    \:::\    \              /::::\    \              /::::\    \           \:::\    \       
     \:::\    \            /::::::\    \            /::::::\    \           \:::\    \      
      \:::\    \          /:::/\:::\    \          /:::/\:::\    \           \:::\    \     
       \:::\    \        /:::/__\:::\    \        /:::/__\:::\    \           \:::\    \    
       /::::\    \      /::::\   \:::\    \       \:::\   \:::\    \          /::::\    \   
      /::::::\    \    /::::::\   \:::\    \    ___\:::\   \:::\    \        /::::::\    \  
     /:::/\:::\    \  /:::/\:::\   \:::\    \  /\   \:::\   \:::\    \      /:::/\:::\    \ 
    /:::/  \:::\____\/:::/__\:::\   \:::\____\/::\   \:::\   \:::\____\    /:::/  \:::\____\
   /:::/    \::/    /\:::\   \:::\   \::/    /\:::\   \:::\   \::/    /   /:::/    \::/    /
  /:::/    / \/____/  \:::\   \:::\   \/____/  \:::\   \:::\   \/____/   /:::/    / \/____/ 
 /:::/    /            \:::\   \:::\    \       \:::\   \:::\    \      /:::/    /          
/:::/    /              \:::\   \:::\____\       \:::\   \:::\____\    /:::/    /           
\::/    /                \:::\   \::/    /        \:::\  /:::/    /    \::/    /            
 \/____/                  \:::\   \/____/          \:::\/:::/    /      \/____/             
                           \:::\    \               \::::::/    /                           
                            \:::\____\               \::::/    /                            
                             \::/    /                \::/    /                             
                              \/____/                  \/____/                              
                                                                                            
```
	
#### 7. Wizard	

This mode consider length of input text to select font

☑️ Support of 95 ASCII characters guaranteed

Keywords : `wizard`, `wiz` & `magic`

ℹ️ New in `Version 2.9`

```pycon
>>> tprint("1","wizard")
    88 
  ,d88 
888888 
    88 
    88 
    88 
    88 
    88 
    88 
    88 
       
            

>>> tprint("1"*5,"wizard")
d88  d88  d88  d88  d88  
 88   88   88   88   88  
 88   88   88   88   88  
 88   88   88   88   88  
 88   88   88   88   88  
d88P d88P d88P d88P d88P 
                         
                         

>>> tprint("1"*15,"wizard")
                                             
                                             
 #  #  #  #  #  #  #  #  #  #  #  #  #  #  # 
## ## ## ## ## ## ## ## ## ## ## ## ## ## ## 
 #  #  #  #  #  #  #  #  #  #  #  #  #  #  # 
 #  #  #  #  #  #  #  #  #  #  #  #  #  #  # 
 #  #  #  #  #  #  #  #  #  #  #  #  #  #  # 
## ## ## ## ## ## ## ## ## ## ## ## ## ## ## 
                                             
                                             
```
	


#### 8. Random Non-ASCII

Randomly select from Non-ASCII fonts.	

Keywords : `random-na`, `rand-na` & `rnd-na`

ℹ️ New in `Version 3.4`

```pycon
>>> tprint("test","random-na")
₮Ɇ₴₮
>>> tprint("test","random-na")
ʇsǝʇ

```

#### 9. Mix Non-ASCII

Randomly mix Non-ASCII fonts.

Keywords : `mix`

ℹ️ New in `Version 3.7`

```pycon
>>> tprint("test","mix")
†Ɛѕ†
>>> tprint("test","mix")
tᏋѕt
>>> tprint("test","mix")
꓄єร꓄
```

⚠️ Non-ASCII fonts are only available in `Font name`, `Random Non-ASCII` and `Mix` modes	


### Typo-tolerance			
<a href="https://en.wikipedia.org/wiki/Levenshtein_distance">Levenshtein distance</a> used in this project. (`Version` >0.9)

```pycon
>>> aprint("happi")  # correct --> aprint("happy"), error < |artname|/2
 ۜ\(סּںסּَ` )/ۜ 
>>> Art=art("birds2222222",number=1) # correct --> Art=art("birds",number=1), error >= |artname|/2
Traceback (most recent call last):
	...
art.art.artError: Invalid art name.
>>> aprint("happi231")  # correct --> aprint("happy"), error < |artname|/2
⎦˚◡˚⎣ 
>>> aprint("happi2312344") # correct --> aprint("happy"), error >= |artname|/2
Traceback (most recent call last):
	...
art.art.artError: Invalid art name.
>>> Art=text2art("test",font="black") # correct --> Art=text2art("test",font="block")
>>> print(Art)

 .----------------.  .----------------.  .----------------.  .----------------.
| .--------------. || .--------------. || .--------------. || .--------------. |
| |  _________   | || |  _________   | || |    _______   | || |  _________   | |
| | |  _   _  |  | || | |_   ___  |  | || |   /  ___  |  | || | |  _   _  |  | |
| | |_/ | | \_|  | || |   | |_  \_|  | || |  |  (__ \_|  | || | |_/ | | \_|  | |
| |     | |      | || |   |  _|  _   | || |   '.___`-.   | || |     | |      | |
| |    _| |_     | || |  _| |___/ |  | || |  |`\____) |  | || |    _| |_     | |
| |   |_____|    | || | |_________|  | || |  |_______.'  | || |   |_____|    | |
| |              | || |              | || |              | || |              | |
| '--------------' || '--------------' || '--------------' || '--------------' |
 '----------------'  '----------------'  '----------------'  '----------------'

>>> tprint("test",font="cybermedum")   # correct --> tprint("test",font="cybermedium")
___ ____ ____ ___ 
 |  |___ [__   |  
 |  |___ ___]  |  
                  
   
```

### Set defaults			
`set_default` function is added in `Version 2.2` in order to change default values.

```pycon
>>> help(set_default)
Help on function set_default in module art.art:

set_default(font='standard', chr_ignore=True, filename='art', print_status=True, overwrite=False, decoration=None, sep='\n')
    Change text2art, tprint and tsave default values.
    
    :param font: input font
    :type font:str
    :param chr_ignore: ignore not supported character
    :type chr_ignore:bool
    :param filename: output file name (only tsave)
    :type filename:str
    :param print_status : save message print flag (only tsave)
    :type print_status:bool
    :param overwrite : overwrite the saved file if true (only tsave)
    :type overwrite:bool
    :param decoration: input decoration
    :type decoration:str
    :param sep: line separator char
    :type sep: str
    :return: None

>>> tprint("test")
 _               _   
| |_   ___  ___ | |_ 
| __| / _ \/ __|| __|
| |_ |  __/\__ \| |_ 
 \__| \___||___/ \__|
                     

>>> set_default(font="italic")
>>> tprint("test")
             
_/  _   _ _/ 
/  (- _)  /  
             

```

ℹ️ Functions error response updated in `Version 0.8`


<table>
	<tr>
		<td align="center">Function</td>
		<td align="center">Normal Output</td>
		<td align="center">Error</td>
	</tr>
	<tr>
		<td align="center">decor</td>
		<td align="center">str</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">art</td>
		<td align="center">str</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">aprint</td>
		<td align="center">None</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">tprint</td>
		<td align="center">None</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">tsave</td>
		<td align="center">{"Status":bool,"Message":str}</td>
		<td align="center">{"Status":bool,"Message":str}</td>
	</tr>
	<tr>
		<td align="center">text2art</td>
		<td align="center">str</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">lprint</td>
		<td align="center">None</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">line</td>
		<td align="center">str</td>
		<td align="center">raise artError</td>
	</tr>
	<tr>
		<td align="center">set_default</td>
		<td align="center">None</td>
		<td align="center">raise artError</td>
	</tr>	
</table> 

### Testing
- Only ASCII fonts and arts :
```
art test
```

- All fonts, arts and decorations :
```
art test2
```

### CLI	
⚠️ [Backward Compatibility] **ART 5.9** is the last version to support this **CLI structure** officially

⚠️ You can use `art` or `python -m art` to run this mode
		
- List of arts :  `art list` or `art arts`
- List of fonts : `art fonts`
- Text : `art text [yourtext] [fontname(optional)]`
- Art : `art shape [artname]` or `art art [artname]`
- Save :  `art save [yourtext] [fontname(optional)]`
- All  :  `art all [yourtext]`

### Telegram bot			

Just send your text to one of these bots. 👇👇👇👇		

<a href="https://t.me/artlib_bot" target="__blank"><img src="https://img.shields.io/badge/Telegram-Bot1-red.svg"></a>			

<a href="https://t.me/textart_robot" target="__blank"><img src="https://img.shields.io/badge/Telegram-Bot2-blue.svg"></a>


### Try ART in your browser

1. ART can be used online in interactive Jupyter Notebooks via the Binder service! Try it out now! :	

[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/sepandhaghighi/art/master)

* Open `FontList.ipynb`, `ArtList.ipynb` and `DecorList.ipynb`
* Edit and execute each part of the notes, step by step from the top panel by run button

2. ART also can be used online in [ascii-generator.site](https://ascii-generator.site/), a Django website by [hermanTenuki](https://github.com/hermanTenuki)	

### Screen record		


<div align="center">
<a href="https://asciinema.org/a/186368" target="_blank"><img src="https://asciinema.org/a/186368.png" /></a>
<p>Screen Record</p>
</div>

* View full font list ([Link1](https://github.com/sepandhaghighi/art/blob/master/FontList.ipynb "Full Font List"),[Link2](https://www.ascii-art.site/FontList.html "Full Font List"))					
* View full art list ([Link1](https://github.com/sepandhaghighi/art/blob/master/ArtList.ipynb "Full Art List"),[Link2](https://www.ascii-art.site/ArtList.html "Full Art List"))
* View full decoration list ([Link1](https://github.com/sepandhaghighi/art/blob/master/DecorList.ipynb "Full Decoration List"),[Link2](https://www.ascii-art.site/DecorList.html "Full Decoration List"))

## Issues & bug reports			

1. Fill an issue and describe it. We'll check it ASAP!
    - Please complete the issue template
2. Discord : [https://discord.com/invite/FAAyq3QJqP](https://discord.com/invite/FAAyq3QJqP)
3. Website : [https://www.ascii-art.site](https://www.ascii-art.site)
4. Email : [<EMAIL>](mailto:<EMAIL> "<EMAIL>")

## References   

1. [FIGlet](http://www.figlet.org/)
2. [1 Line Art](http://1lineart.kulaone.com/#/)
3. [Text to ASCII](http://patorjk.com/software/taag/#p=display&f=Blocks&t=ART)
4. [ASCII Generator](http://www.network-science.de/ascii/)
5. [Asky](https://asky.io/)
6. [Flipyourtext](https://www.flipyourtext.com/)
7. [YayText](https://yaytext.com)
8. [Coolletters](http://www.coolletters.net/)
9. [Instagram Fonts](https://igfonts.io/)
10. [Cool Symbol](https://coolsymbol.com/)
11. [ASCII Moji](http://asciimoji.com/)
12. [SMILEY COOL](https://smiley.cool/)
13. [SPREZZ](https://www.sprezzkeyboard.com/)
14. [Textart4u](http://textart4u.blogspot.com/2013/03/one-line-ascii-text-art.html?m=1)
15. [Chat4o](https://en.chat4o.com/ascii/)
16. [Findwebapp](http://www.findwebapp.com/ascii-art-one-liner/)
17. [Hubpages](https://hubpages.com/technology/one-line-ascii-art-for-twitter)
18. [ASCII-ART](http://www.ascii-art.de/ascii/mno/one_line.txt)
19. [Messletters](https://www.messletters.com/en/)
20. [Webestools](http://www.webestools.com/)
21. [ASCII-emoji](https://github.com/dysfunc/ascii-emoji)
22. [Instagram Fonts2](https://www.instagramfonts.com/)
23. [Emotiworld](http://en.emotiworld.com/)
24. [Fancy Text Pro](https://www.fancytextpro.com/)
25. [Playing Cards in Unicode](https://en.m.wikipedia.org/wiki/Playing_cards_in_Unicode)
26. [Text Generator](https://coolfonts.text-generator.org/)
27. [GOGOTEXT](https://instafontsgen.codesdetail.com)
28. [Fsymbols](https://fsymbols.com/)
29. [Font Copy and Paste](https://www.fontcopypaste.com/)
30. [Mega Cool Text](http://megacooltext.com/)
31. [ToolCalculator](https://www.toolcalculator.com/)
32. [Copy Paste Fonts](https://copy-paste-fonts.top/)


## Show your support
								
<h3>Star this repo</h3>					

Give a ⭐️ if this project helped you!

<h3>Donate to our project</h3>	

If you do like our project and we hope that you do, can you please support us? Our project is not and is never going to be working for profit. We need the money just so we can continue doing what we do ;-) .			

<a href="https://www.ascii-art.site/#support" target="_blank"><img src="https://github.com/sepandhaghighi/art/raw/master/otherfile/donate-button.png" height="90px" width="270px" alt="Art Donation"></a>

<h3>Become a sponsor to ART</h3>

* Contact us at the email first	

<h4>Corporate sponsor</h4>

- **$250** a month
- Your company's logo can be featured on **Readme**
- Intended for small companies


<h4>Mega corporate sponsor</h4>

- **$500** a month
- Your company's logo can be featured on **Readme** and **Website**
- Intended for medium-sized companies

## Acknowledgments
Some parts of the infrastructure for this project are supported by:
<p>
  <a href="https://www.digitalocean.com/">
    <img src="https://opensource.nyc3.cdn.digitaloceanspaces.com/attribution/assets/SVG/DO_Logo_horizontal_blue.svg" width="201px" alt="DigitalOcean">
  </a>
</p>

[Python Software Foundation (PSF)](https://www.python.org/psf/) grants ART library from version **6.3** to **6.6**. [PSF](https://www.python.org/psf/) is the organization behind Python. Their mission is to promote, protect, and advance the Python programming language and to support and facilitate the growth of a diverse and international community of Python programmers.

<a href="https://www.python.org/psf/"><img src="https://github.com/sepandhaghighi/art/raw/master/otherfile/PSF.png" height="55px" alt="Python Software Foundation"></a>

# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [Unreleased]
## [6.5] - 2025-04-12
### Added
- 1 new 1-line-art
	1. bored
- 5 new font
	1. hideki
	2. hisoka
	3. samya
	4. veronika
	5. sunny
### Changed
- `README.md` modified
- `Python 3.6` support dropped
- Test system modified
## [6.4] - 2024-11-26
### Added
- `line` function
- `lprint` function
- 5 new font
	1. abby
	2. clay
	3. neva
	4. orinda
	5. smila
### Changed
- PyPI badge in `README.md`
- GitHub actions are limited to the `dev` and `master` branches
- `Python 3.13` added to `test.yml`
- `README.md` modified
## [6.3] - 2024-09-19
### Added
- `data` directory
- `tests` directory
- 10 new font
	1. lolie
	2. zakia
	3. batman
	4. lord_of_the_ring
	5. kesia
	6. akila
	7. laurine
	8. bianca
	9. callian
	10. awa
### Changed
- `art_param` module changed to `params`
- `art` module split into `errors`, `utils`, and `functions` modules
- `text_dic1`, `text_dic2`, and `text_dic3` renamed to `fonts1`, `fonts2`, and `fonts3`
- `art_dic` renamed to `arts`
- `decor_dic` renamed to `decorations`
- Fonts, arts, and decorations moved to `data` directory
- Tests moved to `tests` directory
- `Python 3.5` support dropped
- Test system modified
- `CONTRIBUTING.md` updated
- `README.md` modified
## [6.2] - 2024-04-22
### Added
- 5 new font
	1. alissa
	2. milka
	3. ozana
	4. shanna
	5. livia
- `decoration_request.yml` template
- `font_request.yml` template
- `one_line_request.yml` template
- `feature_request.yml` template
- `config.yml` for issue template
- `SECURITY.md`
### Changed
- Bug report template modified
- Test system modified
- `README.md` modified
- [DigitalOcean](https://digitalocean.com/) logo added to `README.md`
- `Python 3.12` added to `test.yml`
## [6.1] - 2023-09-20
### Changed
- `font_size_splitter` function modified
- `__detailed_return` parameter added to `art` function
- `__detailed_return` parameter added to `text2art` function
- Random mode name bug fixed
- `__word2art` function modified
- `art` function tail space bug fixed
- `space` parameter bug fixed
- `space` parameter added to `art` function
- `space` parameter added to `aprint` function
- `font_check.py` modified
- `README.md` modified
- `INSTALL.md` modified
## [6.0] - 2023-06-14
### Added
- 6 new font
	1. vaporwave
	2. squiggle1
	3. squiggle2
	4. crazy2
	5. crazy3
	6. emoji
### Changed
- `font_wizard.py` modified
- Drop Python 2.7 and 3.4 support
- `space` parameter added to `tsave` function
- `space` parameter added to `tprint` function
- `space` parameter added to `text2art` function
- `crazy` font renamed to `crazy1`
- Test system modified
- `codecov` removed from `dev-requirements.txt`
- `README.md` modified
- `INSTALL.md` modified
## [5.9] - 2023-03-16
### Added
- 6 new font
	1. small_fancy
	2. boom
	3. cross_jesus
	4. dark_tattoo
	5. lucifer
	6. ascii_roman
### Changed
- Test system modified
- `README.md` modified
- `help_func` function modified
- CLI mode updated
## [5.8] - 2022-11-23
### Added
- 8 new font
	1. russian2
	2. got
	3. vip
	4. crazy
	5. cruse
	6. drippy
	7. stylish
	8. ayda
### Changed
- Test system modified
- `Python 3.11` added to `test.yml`
- `Conda-Forge` installing section added to `INSTALL.md`
- Website changed to https://www.ascii-art.site
### Removed
- `testcov` and `testcov2` modes
## [5.7] - 2022-07-06
### Added
- 5 new font
	1. fancy144
	2. fancy145
	3. fancy146
	4. fancy147
	5. fancy148
- `ASCII_FONTS` list
- `ASCII_ARTS` list
## [5.6] - 2022-04-20
### Added
- 7 new font
	1. fancy141
	2. fancy142
	3. fancy143
	4. tarty6
	5. tarty7
	6. tarty8
	7. tarty9
### Changed
- Test system modified
## [5.5] - 2022-03-02
### Added
- 5 new font
	1. tarty4
	2. tarty5
	3. fancy138
	4. fancy139
	5. fancy140
- Anaconda workflow
### Changed
- `__word2art` function modified
- `font_wizard.py` modified
## [5.4] - 2021-12-22
### Added
- 10 new font
	1. fancy131
	2. fancy132
	3. fancy133
	4. fancy134
	5. fancy135
	6. fancy136
	7. fancy137
	8. tarty1
	9. tarty2
	10. tarty3
- Discord badge
### Changed
- Some fonts width bug fixed
- `test.py` modified
- `font_wizard.py` modified
- `font_check.py` modified
- `art_decor_check.py` modified
- `__word2art` function modified
- `mix_letters` function modified
- `AUTHORS.md` updated
- License updated
- `README.md` modified
- `CONTRIBUTING.md` modified
- `Python 3.10` added to `test.yml`
### Removed
- `greetings.yml`
## [5.3] - 2021-08-30
### Added
- 10 new font
	1. smooth3
	2. flaky
	3. tiny_caps
	4. high_above
	5. handwriting3
	6. fantasy2
	7. manga
	8. upside_down2
	9. cool
	10. instagram
### Changed
- `fantasy` font renamed to `fantasy1`
- `upside_down` font renamed to `upside_down1`
- `sep` parameter added to `text2art`, `tprint`, `tsave` and `set_default` functions
- Default line separator changed from `\r\n` to `\n`
- `__word2art` function modified
- Escaped `\ ` in string literals
## [5.2] - 2021-05-05
### Added
- 10 new 1-line-art
	1. playing cards waterfall
	2. playing cards waterfall (trump)
	3. playing cards hearts waterfall
	4. playing cards diamonds waterfall
	5. playing cards clubs waterfall
	6. playing cards spades waterfall
	7. joker1
	8. joker2
	9. joker3
	10. joker4
- 23 new decorations
	1. fancy87
	2. fancy88
	3. fancy89
	4. fancy90
	5. fancy91
	6. fancy92
	7. fancy93
	8. fancy94
	9. fancy95
	10. heart10
	11. heart11
	12. heart12
	13. heart13
	14. heart14
	15. heart15
	16. star18
	17. star19
	18. star20
	19. star21
	20. star22
	21. star23
	22. star24
	23. star25
- 5 new fonts
	1. old_italic
	2. ninja
	3. tai_viet
	4. subscript2
	5. fancy130
### Changed
- Test system modified
- `subscript` font renamed to `subscript1`
- Logo changed
## [5.1] - 2020-11-30
### Added
- 15 new fonts
	1. fancy120
	2. fancy121
	3. fancy122
	4. fancy123
	5. fancy124
	6. fancy125
	7. fancy126
	8. fancy127
	9. fancy128
	10. fancy129
	11. bud1
	12. bud2
	13. pin1
	14. pin2
	15. sign
- 45 new art
	1. surprised4
	2. surprised5
	3. surprised6
	4. surprised7
	5. surprised8
	6. surprised9
	7. surprised10
	8. surprised11
	9. surprised12
	10. surprised13
	11. surprised14
	12. surprised15
	13. surprised16
	14. surprised17
	15. surprised18
	16. surprised19
	17. surprised20
	18. confused3
	19. confused4
	20. confused5
	21. confused6
	22. confused7
	23. confused8
	24. confused9
	25. confused10
	26. table flip2
	27. table flip3
	28. table flip4
	29. table flip5
	30. table flip6
	31. table flip7
	32. table flip8
	33. table flip9
	34. table flip10
	35. bear2
	36. spider4
	37. cat4
	38. swim
	39. swim2
	40. swim3
	41. yawning
	42. boobs2
	43. airplane3
	44. gun4
	45. cthulhu2
## [5.0] - 2020-11-16
### Added
- 5 new fonts
	1. shasha
	2. fancy116
	3. fancy117
	4. fancy118
	5. fancy119
- 60 new art
	1. hello2
	2. dunno2
	3. dunno3
	4. dunno4
	5. dunno5
	6. happy6
	7. happy7
	8. happy8
	9. happy9
	10. happy10
	11. happy11
	12. happy12
	13. happy13
	14. happy14
	15. happy15
	16. happy16
	17. happy17
	18. happy18
	19. happy19
	20. happy20
	21. happy21
	22. happy22
	23. happy23
	24. happy24
	25. happy25
	26. happy26
	27. happy27
	28. love4
	29. kiss2
	30. kiss3
	31. kissing2
	32. kissing3
	33. kissing4
	34. kissing5
	35. kissing6
	36. cute face2
	37. cute face3
	38. cute face4
	39. cute face5
	40. cute face6
	41. cute face7
	42. cute face8
	43. cute face9
	44. upset
	45. pistols4
	46. pistols5
	47. mad
	48. mad2
	49. mad3
	50. mad4
	51. mad5
	52. mad6
	53. mad7
	54. mad8
	55. mad9
	56. mad10
	57. finger3
	58. finger4
	59. sad5
	60. sad6
### Changed
- `random` mode added to `decor` function
- `both` parameter added to `decor` function
- `art` function modified
- `indirect_font` function modified
## [4.9] - 2020-10-16
### Added
- 10 new fonts
	1. foxy
	2. fancy109
	3. fancy110
	4. fancy111
	5. fancy112
	6. fancy113
	7. fancy114
	8. fancy115
	9. sheqi
	10. fari
### Changed
- `font_wizard.py` script updated
- `README.md` modified
## [4.8] - 2020-09-16
### Added
- 96 new art
	1. angry birds
	2. angry face2
  	3. angry troll
  	4. at what cost
  	5. barf
  	6. basking in glory
  	7. bear GTFO
	8. bear squiting
	9. boxing
	10. breakdown
	11. careless
	12. chasing
	13. cheers
	14. chicken
	15. confused scratch
	16. crazy
	17. creeper
	18. cry face
	19. cry troll
	20. cute face
	21. dance2
	22. dancing
	23. dear god why
	24. devious smile
	25. disagree
	26. discombobulated
	27. dislike1
	28. dislike2
	29. do you even lift bro?
	30. double flip
	31. emo dance
	32. excited
	33. exorcism
	34. eye roll
	35. feel perky
	36. fido
	37. fight
	38. fisticuffs
	39. flip friend
	40. fly away
	41. flying
	42. fuck you2
	43. happy face
	44. happy hug
	45. hitchhicking
	46. hugger
	47. im a hugger
	48. injured
	49. innocent face
	50. japanese lion face
	51. judgemental
	52. judging
	53. kissing
	54. kitty emote
	55. listening to headphones
	56. looking down
	57. love3
	58. meow
	59. no support
	60. opera
	61. peepers
	62. pretty eyes
	63. put the table back
	64. resting my eyes
	65. robot3
	66. running
	67. sad and confused
	68. sad and crying
	69. sad face
	70. satisfied
	71. seal
	72. shark face
	73. shy
	74. sleepy
	75. staring
	76. stranger danger
	77. strut
	78. stunna shades
	79. surprised2
	80. taking a dump
	81. tgif
	82. things that can_t be unseen
	83. tidy up
	84. tired
	85. touchy feely
	86. tripping out
	87. trolling
	88. wave dance
	89. whisling
	90. winnie the pooh
	91. winning
	92. wizard2
	93. worried
	94. yun
	95. zombie2
	96. zoned
### Changed
- `surprised2` 1-line art renamed to `surprised3`
- `art_decor_check.py` script updated
- `font_check.py` script updated 
- `font_wizard.py` script updated
## [4.7] - 2020-05-20
### Added
- `art_decor_check.py` file
- Greeting GitHub actions
- 185 new decorations
	1. angry1
	2. arrow1
	3. arrow2
	4. arrow3
	5. arrow4
	6. arrow5
	7. arrow6
	8. arrow7
	9. arrow8
	10. arrow_wave1
	11. arrow_wave2
	12. ball1
	13. ball2
	14. ball3
	15. bazar1
	16. block1
	17. block2
	18. bow1
	19. bubble
	20. cat1
	21. cat2
	22. cat3
	23. cell1
	24. confused1
	25. confused2
	26. cross1
	27. depressed
	28. diamon3
	29. diamond1
	30. diamond2
	31. egypt1
	32. emotions1
	33. fancy1
	34. fancy2
	35. fancy3
	36. fancy4
	37. fancy5
	38. fancy6
	39. fancy7
	40. fancy8
	41. fancy9
	42. fancy10
	43. fancy11
	44. fancy12
	45. fancy13
	46. fancy14
	47. fancy15
	48. fancy16
	49. fancy17
	50. fancy18
	51. fancy19
	52. fancy20
	53. fancy21
	54. fancy22
	55. fancy23
	56. fancy24
	57. fancy25
	58. fancy26
	59. fancy27
	60. fancy28
	61. fancy29
	62. fancy30
	63. fancy31
	64. fancy32
	65. fancy33
	66. fancy34
	67. fancy35
	68. fancy36
	69. fancy37
	70. fancy38
	71. fancy39
	72. fancy40
	73. fancy41
	74. fancy42
	75. fancy43
	76. fancy44
	77. fancy45
	78. fancy46
	79. fancy47
	80. fancy48
	81. fancy49
	82. fancy50
	83. fancy51
	84. fancy52
	85. fancy53
	86. fancy54
	87. fancy55
	88. fancy56
	89. fancy57
	90. fancy58
	91. fancy59
	92. fancy60
	93. fancy61
	94. fancy62
	95. fancy63
	96. fancy64
	97. fancy65
	98. fancy66
	99. fancy67
	100. fancy68
	101. fancy69
	102. fancy70
	103. fancy71
	104. fancy72
	105. fancy73
	106. fancy74
	107. fancy75
	108. fancy76
	109. fancy77
	110. fancy78
	111. fancy79
	112. fancy80
	113. fancy81
	114. fancy82
	115. fancy83
	116. fancy84
	117. fancy85
	118. fancy86
	119. flame1
	120. flower1
	121. food1
	122. food2
	123. haha
	124. happy1
	125. happy_new_year
	126. hawaii1
	127. hawaii2
	128. heart1
	129. heart2
	130. heart3
	131. heart4
	132. heart5
	133. heart6
	134. heart7
	135. heart8
	136. heart9
	137. line1
	138. line2
	139. line3
	140. line4
	141. love_music
	142. missile1
	143. mountain1
	144. mountain2
	145. mountain3
	146. music1
	147. music2
	148. music3
	149. pencil1
	150. poker1
	151. puzzle1
	152. puzzle2
	153. puzzle3
	154. sad2
	155. sad3
	156. sad4
	157. smile1
	158. snow1
	159. snow2
	160. star3
	161. star4
	162. star5
	163. star6
	164. star7
	165. star8
	166. star9
	167. star10
	168. star11
	169. star12
	170. star13
	171. star14
	172. star15
	173. star16
	174. star17
	175. temple1
	176. title1
	177. tree1
	178. wall1
	179. wave3
	180. wave4
	181. wave5
	182. wave6
	183. wave7
	184. wave8
	185. wave9
### Changed
- `text` parameter removed from `art` and `aprint` functions
- `README.md` modified
- `CONTRIBUTING.md` modified
- MATLAB examples updated
- `kitty2` 1-line art renamed to `kitty`
### Removed
- 5 bipartite 1-line art
	1. love you
	2. message1
	3. message2
	4. musical
	5. text decoration
- 12 duplicate 1-line art
	1. dog
	2. human
	3. mis mujeres
	4. 100$
	5. worm2
	6. stars in my eyes
	7. chainsword
	8. badass
	9. aliens
	10. this guy
	11. kitty1
	12. don fuller
## [4.6] - 2020-03-30
### Added
- `NON_ASCII_ARTS` list
- `decor_dic.py` file
- `codecov.yml` file
- `decor` function
- `decor_list` function
- `DecorList.ipynb` notebook
- 10 new decorations
	1. barcode1
	2. champion1
	3. chess1
	4. lucky1
	5. sad1
	6. soccer1
	7. star1
	8. star2
	9. wave1
	10. wave2
- 20 new fonts
	1. scammer
	2. strikethrough
	3. drako
	4. celtic
	5. fasion
	6. curly
	7. russian
	8. messletters
	9. swirly
	10. dotted
	11. parenthesized
	12. strange
	13. wiggly
	14. hazy
	15. hyves
	16. awcute
	17. slammer
	18. coptic1
	19. coptic2
	20. fancy108
### Changed
- `README.md` modified
- `CONTRIBUTING.md` modified
- MATLAB examples updated
- `setup.py` modified
- `coverage` dependency moved to `extras_require`
- Test system modified
- `random` mode modified
- `test` parameter removed from `font_list` function
- `mode` parameter added to `font_list` and `art_list` functions
- `decoration` parameter added to `text2art`, `tprint` and `tsave` functions
- `\n` support bug fixed
- `tsave` function bug in Windows fixed
### Removed
- `requirements.txt`
## [4.5] - 2020-01-29
### Added
- 35 new fonts
	1. smooth1
	2. smooth2
	3. carrier1
	4. carrier2
	5. cute1
	6. cute2
	7. cute3
	8. cjk
	9. malayalam
	10. lilia
	11. dwarf
	12. awesome
	13. lopioo
	14. cranky
	15. fancy87
	16. fancy88
	17. fancy89
	18. fancy90
	19. fancy91
	20. fancy92
	21. fancy93
	22. fancy94
	23. fancy95
	24. fancy96
	25. fancy97
	26. fancy98
	27. fancy99
	28. fancy100
	29. fancy101 
	30. fancy102
	31. fancy103
	32. fancy104
	33. fancy105
	34. fancy106
	35. fancy107
- 43 new 1-line art
	1. fish6
	2. loch ness monster
	3. mouse5
	4. mouse6
	5. mouse7
	6. sheep
	7. pie fight
	8. rose5
	9. rose6
	10. rose7
	11. concerned
	12. happy4
	13. happy5
	14. coffee2
	15. coffee3
	16. coffee4
	17. headphone3
	18. carpet roll
	19. infinity
	20. computer mouse
	21. volcano1
	22. volcano2
	23. volcano3
	24. squigle with spirals
	25. palm tree
	26. crotch shot
	27. vagina
	28. stealth fighter
	29. tent1
	30. tent2
	31. power lines
	32. tree stump
	33. hammer
	34. arrow2
	35. sword10
	36. fish skeleton2
	37. fork
	38. pipe
	39. dead guy
	40. dead girl
	41. dead child
	42. dude glasses1
	43. dude glasses2
### Changed
- `README.md` modified
- `INSTALL.md` modified
- `coffee` 1-line art renamed to `coffee1`
- `arrow` 1-line art renamed to `arrow1`
- `fish skeleton` 1-line art renamed to `fish skeleton1`
## [4.4] - 2019-12-26
### Added
- 68 new 1-line art
	1. smile
	2. toungue out1
	3. toungue out2
	4. bad hair1
	5. bad hair2
	6. woops
	7. screaming
	8. full mouth
	9. cussing
	10. ready to cry
	11. crying2
	12. druling1
	13. druling2
	14. big kiss
	15. french kiss
	16. wink
	17. big smile
	18. smirk
	19. confused1
	20. confused2
	21. pursing lips
	22. shocked2
	23. really sad
	24. baseball fan
	25. angel2
	26. really mad
	27. licking lips
	28. buck teeth
	29. surprised1
	30. alien
	31. sunglasses2
	32. don king
	33. devilish smile
	34. devilish grin
	35. clowning
	36. bugs bunny
	37. kitty2
	38. fish skeleton
	39. snowman2
	40. marshmallows
	41. nerd
	42. mouse4
	43. pistols3
	44. rock on2
	45. love in my eye2
	46. love in my eye3
	47. sad3
	48. sad4
	49. headphone2
	50. cigarette1
	51. spider3
	52. barcode2
	53. equalizer
	54. boombox2
	55. surprised2
	56. playing cards
	57. playing cards hearts
	58. playing cards diamonds
	59. playing cards spades
	60. playing cards clubs
	61. spade bold
	62. heart bold
	63. diamond bold
	64. club bold
	65. spade regular
	66. heart regular
	67. diamond regular
	68. club regular
- MATLAB examples
### Changed
- Bipartite art deprecated
- `crying` 1-line art renamed to `crying1`
- `shocked` 1-line art renamed to `shocked1`
- `angel` 1-line art renamed to `angel1`
- `sunglasses` 1-line art renamed to `sunglasses1`
- `kitty` 1-line art renamed to `kitty1`
- `snowman` 1-line art renamed to `snowman1`
- `barcode` 1-line art renamed to `barcode1`
- `cigaret` 1-line art renamed to `cigarette3`
- `cigarette` 1-line art renamed to `cigarette2`
- `headphone` 1-line art renamed to `headphone1`
- `rock on` 1-line art renamed to `rock on1`
- `boom box` 1-line art renamed to `boombox1`
- `love in my eye` 1-line art renamed to `love in my eye1`
- `CONTRIBUTING.md` modified
- `TEST_FILTERED_FONTS` renamed to `NON_ASCII_FONTS`
- `RANDOM_FILTERED_FONTS` and `NON_ASCII_FONTS` lists optimized
- `README.md` modified
- `appveyor.yml` modified
- `INSTALL.md` modified
- `wizard` mode modified
## [4.3] - 2019-12-05
### Added
- 35 new 1-line art
	1. cup1
	2. cup2
	3. gun2
	4. gun3
	5. fish3
	6. fish4
	7. fish5
	8. sword6
	9. sword7
	10. sword8
	11. sword9
	12. knife2
	13. barcode
	14. loading1
	15. loading3
	16. loading4
	17. loading5
	18. loading6
	19. bat2
	20. band aid
	21. mouse2
	22. mouse3
	23. cat3
	24. needle2
	25. rose4
	26. electrocardiogram1
	27. electrocardiogram2
	28. electrocardiogram3
	29. electrocardiogram4
	30. snail2
	31. american money2
	32. american money3
	33. american money4
	34. american money5
	35. cassette2
- 10 new fonts
	1. sarah
	2. hippie
	3. minion
	4. greek_legends
	5. funky_fresh
	6. atlantic
	7. foreign_friends
	8. native_lands
	9. sunday_cuddle
	10. chinese_mafia
### Changed
- `gun` 1-line art renamed to `gun1`
- `knife` 1-line art renamed to `knife1`
- `loading` 1-line art renamed to `loading2`
- `bat` 1-line art renamed to `bat1`
- `mouse` 1-line art renamed to `mouse1`
- `needle` 1-line art renamed to `needle2`
- `snail` 1-line art renamed to `snail1`
- `american money` 1-line art renamed to `american money1`
- `cassette` 1-line art renamed to `cassette1`
- Test system modified
## [4.2] - 2019-10-30
### Added
- 14 new fonts
	1. smallcaps3
	2. fraktur2
	3. bold_fraktur
	4. bold_script
	5. handwriting1
	6. handwriting2
	7. rusify
	8. monospace
	9. dirty2
	10. knight2
	11. h4k3r
	12. thin3
	13. tiny2
	14. upside_down
- `FONT_NAMES` variable
- `ART_NAMES` variable
### Changed
- Namespace optimized
- Website changed to https://www.4r7.ir
## [4.1] - 2019-09-26
### Added
- 20 new fonts
	1. fancy75
	2. fancy76
	3. fancy77
	4. fancy78
	5. love1
	6. fancy79
	7. fancy80
	8. fancy81
	9. fancy82
	10. fancy83
	11. fancy84
	12. fancy85
	13. fancy86
	14. contouring3
	15. contouring4
	16. love2
	17. black_square
	18. white_square
	19. black_bubble
	20. paranormal
- `get_font_dic` function
### Changed
- `font_check.py` modified
- `font_wizard.py` modified
- `CONTRIBUTING.md` modified
## [4.0] - 2019-09-12
### Added
- `install.sh`
- `autopep8.sh`
- 21 new fonts
	1. fancy56
	2. fancy57
	3. fancy58
	4. fancy59
	5. fancy60
	6. fancy61
	7. fancy62
	8. fancy63
	9. fanyc64
	10. fancy65
	11. fancy66
	12. fancy67
	13. fancy68
	14. fancy69
	15. fanyc70
	16. fancy71
	17. fancy72
	18. fancy73
	19. fancy74
	20. contouring1
	21. contouring2
### Changed
- `test.sh` moved to `.travis` folder
- `font_wizard.py` updated
- `font_check.py` updated
- `tsave` function bugs fixed
- `aprint` and `tprint` functions modified
- Test system modified
- `overwrite` parameter added to `tsave` and `set_default` functions
- `README.md` modified
- `CONTRIBUTING.md` modified
## [3.9] - 2019-08-21
### Added
- `__version__` variable
- `font_wizard.py`
- 15 new fonts
	1. fancy41
	2. fancy42
	3. fancy43
	4. fancy44
	5. fancy45
	6. fancy46
	7. fancy47
	8. fancy48
	9. fancy49
	10. fancy50
	11. fancy51
	12. fancy52
	13. fancy53
	14. fancy54
	15. fancy55
### Changed
- `VERSION` variable renamed to `ART_VERSION`
- Some fonts moved to `text_dic3.py`
- `ISSUE_TEMPLATE.md` modified
- `CONTRIBUTING.md` modified
## [3.8] - 2019-07-19
### Added
- 11 new fonts
	1. fancy31
	2. fancy32
	3. fancy33
	4. fancy34
	5. symbols
	6. fancy35
	7. fancy36
	8. fancy37
	9. fancy38
	10. fancy39
	11. fancy40
- 20 new 1-line art
	1. acid
	2. arrowhead
	3. awkward
	4. because
	5. blackeye
	6. catlenny
	7. dab
	8. damnyou
	9. depressed
	10. dunno
	11. eeriemob
	12. envelope
	13. fart
	14. finn
	15. frown
	16. glitter
	17. gotit
	18. hello
	19. loading
	20. meep
### Changed
- `dev-requirements.txt` modified
## [3.7] - 2019-06-24
### Added
- 3 new fonts
	1. fancy28
	2. fancy29
	3. fancy30
- `mix` mode
### Changed
- Duplication test added to`font_check.py`
- `README.md` modified
- fancy11 and fancy14 fonts updated
- `indirect_font` function modified
### Removed
- 9 duplicate fonts
	1. magical
	2. morse2
	3. fire_font-k
	4. paranormal
	5. battle_s
	6. ntgreek
	7. peaksslant
	8. sorcerer
	9. nancyj-improved
## [3.6] - 2019-05-15
### Added
- 20 new fonts
	1. fancy8
	2. fancy9
	3. fancy10
	4. fancy11
	5. fancy12
	6. fancy13
	7. fancy14
	8. fancy15
	9. fancy16
	10. fancy17
	11. fancy18
	12. fancy19
	13. fancy20
	14. fancy21
	15. fancy22
	16. fancy23
	17. fancy24
	18. fancy25
	19. fancy26
	20. fancy27
### Changed
- Drop Python 3.3 support
## [3.5] - 2019-05-04
### Added
- 15 new fonts
	1. magical
	2. paranormal
	3. sorcerer
	4. special
	5. dirty
	6. knight
	7. thin2
	8. tiny
	9. fancy1
	10. fancy2
	11. fancy3
	12. fancy4
	13. fancy5
	14. fancy6
	15. fancy7
## [3.4] - 2019-04-20
### Added
- 7 new fonts
	1. white_bubble
	2. smallcaps2
	3. superscript
	4. subscript
	5. full_width
	6. currency
	7. antrophobia
- `text2art` function support of `\n`
- `rnd-na` mode
### Changed
- `tprint` function modified
- `tsave` function modified
- `README.md` modified
- `random` mode modified
## [3.3] - 2019-04-10
### Added
- 3 new fonts
	1. mirror
	2. flip
	3. mirror_flip
- 40 new 1-line art
	1. dead eyes
	2. satan
	3. bomb
	4. sleepy coffee
	5. high five
	6. wtf dude?
	7. fungry
	8. derp
	9. drwoning
	10. almost cared
	11. yessir
	12. coffee now
	13. round cat
	14. squee
	15. round bird
	16. hoxom
	17. squid
	18. man spider
	19. spell cast
	20. jazz musician
	21. myan cat
	22. gimme
	23. crab
	24. playing in snow
	25. sunglasses
	26. mini penis
	27. victory
	28. don fuller
	29. dgaf
	30. ryans dick
	31. eds dick
	32. point
	33. afraid
	34. laughing
	35. energy
	36. drunkenness
	37. error
	38. penis2
	39. exchange
	40. singing2
- `test2.py`
- `font_check.py`
### Changed
- `test_mode` parameter added to `font_list` function
- `all` flag bug fixed
- `__main__.py` modified
- Warnings and errors messages moved to `art_param.py`
- Art random mode modified
## [3.2] - 2019-04-02
### Added
- 30 new 1-line art
	1. shrug
	2. flex
	3. this guy
	4. why
	5. meditation
	6. kitty
	7. pac man
	8. emo
	9. shark attack
	10. shocked
	11. monocle
	12. piggy
	13. camera
	14. what??
	15. neo
	16. lenny
	17. snowing
	18. dick
	19. wizard
	20. wat 
	21. chu
	22. butt
	23. sophie
	24. joy
	25. bautista
	26. smooth
	27. fuckall
	28. penis
	29. wat-wat
	30. fail
- `pydocstyle` test
### Changed
- Docstrings modified
- Some fonts lowercase bug fixed
- Font random modes modified
- `test_mode` parameter removed from `font_list` function

## [3.1] - 2019-03-06
### Added
- 30 new 1-line art
	1. rose3
	2. cigaret
	3. oar
	4. sword5
	5. car
	6. hacksaw
	7. canoe
	8. spider1
	9. machinegun
	10. roadblock
	11. aliens
	12. marge simpson
	13. homer simpson
	14. bat
	15. superman logo
	16. dna sample
	17. mail box
	18. ufo1
	19. ufo2
	20. spider2
	21. american money
	22. british money
	23. teepee
	24. heart3
	25. angel
	26. shark
	27. regular ass
	28. fat ass
	29. kiss my ass
	30. devil
- 15 new fonts
	1. future_1
	2. future_2
	3. future_3
	4. future_4
	5. future_5
	6. future_6
	7. future_7
	8. future_8
	9. gauntlet
	10. ghost_bo
	11. grand_pr
	12. green_be
	13. hades
	14. heavy_me
	15. nfi1
### Changed
- `README.md` modified
- `art` function typo-tolerance threshold modified

## [3.0] - 2019-02-11
### Added
- 20 new fonts
	1. druid
	2. e_fist
	3. ebbs_1
	4. ebbs_2
	5. eca
	6. faces_of
	7. fair_mea
	8. fairligh
	9. fantasy
	10. fbr12
	11. fbr1
	12. fbr2
	13. fbr_stri
	14. fbr_tilt
	15. finalass
	16. fireing
	17. flyn_sh
	18. fp1
	19. fp2
	20. funky_dr
- `CODE_OF_CONDUCT.md`
- `PULL_REQUEST_TEMPLATE.md`
- `ISSUE_TEMPLATE.md`
- `vulture` test
- `bandit` test
### Changed
- `README.md` modified
- `CONTRIBUTING.md` modified
- `font_map` renamed to `FONT_MAP`
- `font_counter` renamed to `FONT_COUNTER`
- `art_counter` renamed to `ART_COUNTER`
- Constants and parameters moved to `art_param.py`


## [2.9] - 2019-01-24
### Added
- 20 new fonts
	1. aquaplan
	2. ascii
	3. c_consen
	4. clb6x10
	5. clb8x10
	6. clb8x8
	7. cli8x8
	8. clr4x6
	9. clr5x10
	10. clr5x6
	11. clr5x8
	12. clr6x10
	13. clr6x6
	14. clr6x8
	15. clr7x8
	16. clr8x10
	17. clr8x8
	18. coil_cop
	19. com_sen
	20. c_ascii
- `wizard` mode
- Interactive notebooks section (binder)
- `.coveragerc`
- `version_check.py`
### Changed
- `README.md` modified
- Minor bug in coverage system fixed
- `.travis.yml` modified
- `font_size_splitter` function thresholds fixed

## [2.8] - 2019-01-01
### Added
- 20 new fonts
	1. zone7
	2. zig_zag
	3. yie_ar_k
	4. yie-ar
	5. xtty
	6. war_of_w
	7. vortron
	8. utopia
	9. utopiai
	10. utopiabi
	11. utopiab
	12. usa_pq
	13. usa
	14. unarmed
	15. ugalympi
	16. ucf_fan
	17. type_set
	18. twin_cob
	19. tsn_base
	20. z-pilot
- `rnd-small` mode
- `rnd-medium` mode
- `rnd-large` mode
- `rnd-xlarge` mode
### Changed
- `README.md` modified

## [2.7] - 2018-12-23
### Added
- 30 new fonts
	1. heroboti
	2. high_noo
	3. hills
	4. home_pak
	5. house_of
	6. hypa_bal
	7. hyper
	8. inc_raw
	9. italics
	10. kgames_i
	11. kik_star
	12. krak_out
	13. 4x4_offr
	14. 5x7
	15. 5x8
	16. 64f1
	17. 6x10
	18. 6x9
	19. a_zooloo
	20. asc
	21. assalt_m
	22. asslt_m
	23. atc
	24. atc_gran
	25. battle_s
	26. battlesh
	27. baz_bil
	28. beer_pub
	29. c1
	30. c2
- `art_profile.py`
### Changed
- Docstring modified
- `B1FF` font bug fixed
- `ICL-1900` font bug fixed
- `AK-47` 1-line art bug fixed
- `I kill you` 1-line art bug fixed

## [2.6] - 2018-12-05
### Added
- 20 new fonts
	1. xbrite
	2. xbriteb
	3. xbritei
	4. xbritebi
	5. xchartr
	6. xchartri
	7. xcour
	8. xcourb
	9. xcourbi
	10. xcouri
	11. xhelv
	12. xhelvb
	13. xhelvbi
	14. xhelvi
	15. xsans
	16. xsansb
	17. xsansbi
	18. xsansi
	19. xtimes
	20. xttyb
- CLI `testcov` flag
### Changed
- `requirements.txt` modified
- `INSTALL.md` modified
- CLI `test` flag modified

## [2.5] - 2018-12-02
### Added
- 15 new fonts
	1. char1
	2. char2
	3. char3
	4. char4
	5. charact1
	6. charact2
	7. charact3
	8. charact4
	9. charact5
	10. charact6
	11. characte
	12. chartr
	13. chartri
	14. 1943
	15. advenger
- `dev-requirements.txt`

### Changed
- `Python 3.7` added to `.travis.yml` & `appveyor.yml`
- `font_list` modified
- `README.md` modified
- `INSTALL.md` modified

## [2.4] - 2018-10-30
### Added
- 10 new fonts
	1. alpha
	2. alligator3
	3. amc3liv1
	4. ascii_new_roman
	5. B1FF
	6. dwhistled
	7. eftiwall
	8. fire_font-k
	9. fire_font-s
	10. gradient
### Changed
- `requirements.txt` modified

## [2.3] - 2018-09-30
### Added
- 35 new fonts
	1. nancyj-improved
	2. nscript
	3. ntgreek
	4. nvscript
	5. octal
	6. oldbanner
	7. os2
	8. peaksslant
	9. puzzle
	10. rammstein
	11. red_phoenix
	12. runyc
	13. santaclara
	14. shimrod
	15. smallcaps
	16. smpoison
	17. soft
	18. spliff
	19. stacey
	20. stampate
	21. stforek
	22. sub-zero
	23. swampland
	24. sweet
	25. ticks
	26. ticksslant
	27. tiles
	28. tsalagi
	29. tubular
	30. twisted
	31. varsity
	32. wavy
	33. wetletter
	34. whimsy
	35. wow

## [2.2] - 2018-09-24
### Added
- 15 new fonts
	1. ICL-1900
	2. impossible
	3. jacky
	4. katakana
	5. keyboard
	6. knob
	7. lildevil
	8. lineblocks
	9. merlin1
	10. merlin2
	11. modular
	12. morse
	13. morse2
	14. moscow
	15. muzzle
- `set_default` function
- `randart` function

## [2.1] - 2018-09-18
### Added
- 15 new fonts
	1. fraktur
	2. funface
	3. funfaces
	4. georgi16
	5. georgia11
	6. ghost
	7. ghoulish
	8. glenyn
	9. graceful
	10. greek
	11. heartleft
	12. heartright
	13. henry3d
	14. horizontalleft
	15. horizontalright
### Changed
- `setuptools` removed from `requirements.txt`
- `OSX` env added to `.travis.yml`
- Test cases modified
- `text2art` bug  in OSX fixed

## [2.0] - 2018-08-30
### Added
- 20 new fonts
	1. bigfig
	2. bolger
	3. braced
	4. bright
	5. broadway
	6. cards
	7. chiseled
	8. cola
	9. crawford
	10. cricket
	11. DANC4
	12. dancingfont
	13. decimal
	14. defleppard
	15. dietcola
	16. flipped
	17. double
	18. doubleshorts
	19. eftipiti
	20. filter
- Font list HTML page
- Art list HTML page

## [1.9] - 2018-08-23
### Added
- 20 new fonts
	1. cybersmall
	2. gothic
	3. rev
	4. smtengwar
	5. term
	6. 1row
	7. 3d_diagonal
	8. 4max
	9. amc3line
	10. amcrazor
	11. amcaaa01
	12. amcneko
	13. amcrrazo2
	14. amcslash
	15. amcthin
	16. amctubes
	17. amcun1
	18. arrows
	19. bear
	20. benjamin

## [1.8] - 2018-08-06
### Added
- 30 new 1-line art
	1. westbound fish
	2. telephone
	3. 9/11 truth
	4. spear
	5. srs face
	6. this is areku
	7. robot boy
	8. med man
	9. angry
	10. badass
	11. zoidberg
	12. eastbound fish
	13. kilroy was here
	14. gtalk fit
	15. thanks
	16. dalek
	17. sean the sheep
	18. kablewee
	19. i dont care
	20. slenderman
	21. john lennon
	22. peace yo
	23. punch
	24. russian boobs
	25. fuck off
	26. man tears
	27. robber
	28. facepalm
	29. yo
	30. party time

## [1.7] - 2018-08-01
### Added
- 30 new 1-line art
	1. crayons
	2. stars in my eyes
	3. fish invasion
	4. bender
	5. musical
	6. sunny day
	7. happy birthday 1
	8. line brack
	9. med
	10. melp1
	11. happy3
	12. happy square
	13. snowman
	14. melp2
	15. i kill you
	16. jaymz
	17. text decoration
	18. long rose
	19. kirbay dance
	20. death star defense team
	21. chainsword
	22. boobies
	23. dancing people
	24. dance
	25. pictou
	26. polar bear
	27. go away bear
	28. charly
	29. train
	30. spot
### Changed
- `car race` 1-line art fixed

## [1.6] - 2018-07-10
### Added
- 30 new 1-line art
	1. sexy symbol
	2. barbell
	3. sniper rifle
	4. being draged
	5. possessed
	6. jokeranonimous
	7. epic gun
	8. love
	9. love2
	10. eric
	11. puls
	12. sky free
	13. smug bastard
	14. tie-fighter
	15. kyubey
	16. dancee
	17. mtmtika
	18. ak-47
	19. eaten apple
	20. huhu
	21. faydre
	22. domino
	23. honeycute
	24. superman
	25. worm2
	26. jokeranonimous2
	27. kyubey2
	28. nose2
	29. hell yeah
	30. roke

## [1.5] - 2018-06-18
### Added
- 30 new 1-line art
	1. sorreh bro
	2. yolo
	3. formula 1 car
	4. dummy
	5. rope
	6. rare
	7. chess pieces
	8. sparkling heart
	9. weather
	10. stars2
	11. upsidedown
	12. nathan
	13. cat smile
	14. old lady boobs
	15. glasses2
	16. religious
	17. sniperstars
	18. kokain
	19. bagel
	20. crying
	21. angry2
	22. 3
	23. 5
	24. fuck you
	25. head shot
	26. metal
	27. killer
	28. fu
	29. ankush
	30. owlkin
### Changed
- CLI `art` flag
- CLI `list` flag
## [1.4] - 2018-06-14
### Added
- 15 new fonts
	1. letters
	2. lockergnome
	3. madrid
	4. marquee
	5. mike
	6. mini
	7. nancyj-fancy
	8. nancyj-underlined
	9. pepper
	10. poison
	11. rot13
	12. short
	13. small
	14. tengwar
	15. big
- 11 new 1-line art
	1. teddy
	2. dice
	3. bee
	4. ukulele
	5. perky
	6. snail
	7. decorate
	8. kirby
	9. mango
	10. bunny
	11. kiss
- `random` 1-line art mode
### Changed
- Minor bug in `text2art` random range fixed
- `README.md` modified
- `aprint_test` function renamed to `art_list`
- `setup.py` modified

## [1.3] - 2018-06-08
### Added
- 10 new fonts
	1. eftitalic
	2. eftiwater
	3. fourtops
	4. goofy
	5. hollywood
	6. invita
	7. italic
	8. jazmine
	9. lcd
	10. lean
- `random` font mode

## [1.2] - 2018-06-04
### Added
- 8 new fonts
	1. barbwire
	2. bigchief
	3. binary
	4. bubble
	5. calgphy2
	6. cygnet
	7. diamond
	8. eftifont
### Changed
- Test cases modified

## [1.1] - 2018-05-25
### Added
- 5 new fonts
	1. peaks
	2. pawp
	3. o8
	4. nipples
	5. maxfour
- 15 new 1-line art
	1. guitar
	2. rocket
	3. ghost
	4. hal
	5. cthulhu
	6. sat
	7. what
	8. king
	9. tron
	10. homer
	11. fox
	12. singing
	13. atish
	14. zable
	15. trumpet
- CLI description
### Changed
- `README.md` modified
- Test cases modified
- File name bug in UNIX fixed
- `CHANGELOG.md` modified

## [1.0] - 2018-05-20
### Added
- 15 new fonts										
	1. tinker-toy
	2. straight
	3. stampatello
	4. smslant
	5. smshadow
	6. smscript
	7. smkeyboard
	8. smisome1
	9. slscript
	10. slide
	11. sblood
	12. rozzo
	13. pyramid
	14. puffy
	15. pebbles
- Typo-tolerance system
### Changed
- `README.md` modified
- Test cases modified

## [0.9] - 2018-05-08
### Added
- 10 new fonts			
	1. weird
	2. univers
	3. twopoint
	4. trek
	5. tombstone
	6. threepoint
	7. thick
	8. tanja
	9. swan
	10. stellar 

### Changed
- `MANIFEST.in` modified
- `INSTALL.md` modified

## [0.8] - 2018-03-13
### Added
- `CHANGELOG.md`

### Changed
- Functions error response
- `README.md` modified
- `tsave` function extension bug fixed
- `text2art` response bug fixed ("\r\n")
- `setup.py` modified

## [0.7] - 2018-01-20
### Added
- 10 new fonts		
	1. acrobatic
	2. alligator
	3. alligator2
	4. block2
	5. caligraphy
	6. computer
	7. digital
	8. doh
	9. eftirobot
	10. graffiti

## [0.6] - 	2018-01-09
### Added
- `all` flag

## [0.5] - 2017-12-05
### Added
- 10 new fonts
	1. 3-d
	2. 3x5
	3. 5lineoblique
	4. alphabet
	5. banner3-D
	6. banner3
	7. banner4
	8. bell
	9. catwalk
	10. colossal

## [0.4] - 2017-11-11
### Added
- Telegram bot


### Changed
- Universal text format added for Unix & Windows



## [0.3] - 	2017-10-28
### Added
- Save function (tsave)
- `chr_ignore` flag
- 10 new fonts
	1. banner
	2. avatar
	3. basic
	4. bulbhead
	5. chunky
	6. coinstak
	7. contessa
	8. contrast
	9. cyberlarge
	10. cybermedium
	11. doom
	12. dotmatrix
	13. drpepper
	14. epic
	15. fuzzy
	16. isometric1
	17. isometric2
	18. isometric3
	19. isometric4
	20. larry3d
	21. nancyj
	22. ogre
	23. rectangles
	24. roman
	25. rounded
	26. rowancap
	27. script
	28. serifcap
	29. shadow
	30. slant
	31. speed
	32. starwars
	33. stop
	34. thin
	35. usaflag
### Changed
- Space bug fixed


## [0.2] - 	2017-10-10
### Added
- Standard font
### Changed
- Minor bugs fixed

## [0.1] - 2017-10-04
### Added
- Block font
- 1-Line art
- CLI commands

[Unreleased]: https://github.com/sepandhaghighi/art/compare/v6.5...dev
[6.5]: https://github.com/sepandhaghighi/art/compare/v6.4...v6.5
[6.4]: https://github.com/sepandhaghighi/art/compare/v6.3...v6.4
[6.3]: https://github.com/sepandhaghighi/art/compare/v6.2...v6.3
[6.2]: https://github.com/sepandhaghighi/art/compare/v6.1...v6.2
[6.1]: https://github.com/sepandhaghighi/art/compare/v6.0...v6.1
[6.0]: https://github.com/sepandhaghighi/art/compare/v5.9...v6.0
[5.9]: https://github.com/sepandhaghighi/art/compare/v5.8...v5.9
[5.8]: https://github.com/sepandhaghighi/art/compare/v5.7...v5.8
[5.7]: https://github.com/sepandhaghighi/art/compare/v5.6...v5.7
[5.6]: https://github.com/sepandhaghighi/art/compare/v5.5...v5.6
[5.5]: https://github.com/sepandhaghighi/art/compare/v5.4...v5.5
[5.4]: https://github.com/sepandhaghighi/art/compare/v5.3...v5.4
[5.3]: https://github.com/sepandhaghighi/art/compare/v5.2...v5.3
[5.2]: https://github.com/sepandhaghighi/art/compare/v5.1...v5.2
[5.1]: https://github.com/sepandhaghighi/art/compare/v5.0...v5.1
[5.0]: https://github.com/sepandhaghighi/art/compare/v4.9...v5.0
[4.9]: https://github.com/sepandhaghighi/art/compare/v4.8...v4.9
[4.8]: https://github.com/sepandhaghighi/art/compare/v4.7...v4.8
[4.7]: https://github.com/sepandhaghighi/art/compare/v4.6...v4.7
[4.6]: https://github.com/sepandhaghighi/art/compare/v4.5...v4.6
[4.5]: https://github.com/sepandhaghighi/art/compare/v4.4...v4.5
[4.4]: https://github.com/sepandhaghighi/art/compare/v4.3...v4.4
[4.3]: https://github.com/sepandhaghighi/art/compare/v4.2...v4.3
[4.2]: https://github.com/sepandhaghighi/art/compare/v4.1...v4.2
[4.1]: https://github.com/sepandhaghighi/art/compare/v4.0...v4.1
[4.0]: https://github.com/sepandhaghighi/art/compare/v3.9...v4.0
[3.9]: https://github.com/sepandhaghighi/art/compare/v3.8...v3.9
[3.8]: https://github.com/sepandhaghighi/art/compare/v3.7...v3.8
[3.7]: https://github.com/sepandhaghighi/art/compare/v3.6...v3.7
[3.6]: https://github.com/sepandhaghighi/art/compare/v3.5...v3.6
[3.5]: https://github.com/sepandhaghighi/art/compare/v3.4...v3.5
[3.4]: https://github.com/sepandhaghighi/art/compare/v3.3...v3.4
[3.3]: https://github.com/sepandhaghighi/art/compare/v3.2...v3.3
[3.2]: https://github.com/sepandhaghighi/art/compare/v3.1...v3.2
[3.1]: https://github.com/sepandhaghighi/art/compare/v3.0...v3.1
[3.0]: https://github.com/sepandhaghighi/art/compare/v2.9...v3.0
[2.9]: https://github.com/sepandhaghighi/art/compare/v2.8...v2.9
[2.8]: https://github.com/sepandhaghighi/art/compare/v2.7...v2.8
[2.7]: https://github.com/sepandhaghighi/art/compare/v2.6...v2.7
[2.6]: https://github.com/sepandhaghighi/art/compare/v2.5...v2.6
[2.5]: https://github.com/sepandhaghighi/art/compare/v2.4...v2.5
[2.4]: https://github.com/sepandhaghighi/art/compare/v2.3...v2.4
[2.3]: https://github.com/sepandhaghighi/art/compare/v2.2...v2.3
[2.2]: https://github.com/sepandhaghighi/art/compare/v2.1...v2.2
[2.1]: https://github.com/sepandhaghighi/art/compare/v2.0...v2.1
[2.0]: https://github.com/sepandhaghighi/art/compare/v1.9...v2.0
[1.9]: https://github.com/sepandhaghighi/art/compare/v1.8...v1.9
[1.8]: https://github.com/sepandhaghighi/art/compare/v1.7...v1.8
[1.7]: https://github.com/sepandhaghighi/art/compare/v1.6...v1.7
[1.6]: https://github.com/sepandhaghighi/art/compare/v1.5...v1.6
[1.5]: https://github.com/sepandhaghighi/art/compare/v1.4...v1.5
[1.4]: https://github.com/sepandhaghighi/art/compare/v1.3...v1.4
[1.3]: https://github.com/sepandhaghighi/art/compare/v1.2...v1.3
[1.2]: https://github.com/sepandhaghighi/art/compare/v1.1...v1.2
[1.1]: https://github.com/sepandhaghighi/art/compare/v1.0...v1.1
[1.0]: https://github.com/sepandhaghighi/art/compare/v0.9...v1.0
[0.9]: https://github.com/sepandhaghighi/art/compare/v0.8...v0.9
[0.8]: https://github.com/sepandhaghighi/art/compare/v0.7...v0.8
[0.7]: https://github.com/sepandhaghighi/art/compare/v0.6...v0.7
[0.6]: https://github.com/sepandhaghighi/art/compare/v0.5...v0.6
[0.5]: https://github.com/sepandhaghighi/art/compare/v0.4...v0.5
[0.4]: https://github.com/sepandhaghighi/art/compare/v0.3...v0.4
[0.3]: https://github.com/sepandhaghighi/art/compare/v0.2...v0.3
[0.2]: https://github.com/sepandhaghighi/art/compare/v0.1...v0.2
[0.1]: https://github.com/sepandhaghighi/art/compare/1e238cd...v0.1



