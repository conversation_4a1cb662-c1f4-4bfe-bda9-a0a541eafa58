import os
import glob
import datetime

# DATE TIME folder name
date_time_folder = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
def delete_csv_and_xlsx_files(folder_path):
    # Check if the input folder exists
    if not os.path.exists(folder_path):
        print(f"Warning: Input folder {folder_path} does not exist")
        return

    # create datetime folder if it doesn't exist
    datetime_folder_path = os.path.join(folder_path, date_time_folder)
    try:
        if not os.path.exists(datetime_folder_path):
            os.makedirs(datetime_folder_path, exist_ok=True)
            print(f"Created backup folder: {datetime_folder_path}")
    except Exception as e:
        print(f"Error creating backup folder {datetime_folder_path}: {e}")
        return

    # move all csv and xlsx files to datetime folder
    csv_files = glob.glob(os.path.join(folder_path, "*.csv"))
    xlsx_files = glob.glob(os.path.join(folder_path, "*.xlsx"))

    print(f"Found {len(csv_files)} CSV files and {len(xlsx_files)} XLSX files to move")

    for file in csv_files:
        try:
            destination = os.path.join(folder_path, date_time_folder, os.path.basename(file))
            os.rename(file, destination)
            print(f"Moved: {os.path.basename(file)}")
        except Exception as e:
            print(f"Error moving {file}: {e}")

    for file in xlsx_files:
        try:
            destination = os.path.join(folder_path, date_time_folder, os.path.basename(file))
            os.rename(file, destination)
            print(f"Moved: {os.path.basename(file)}")
        except Exception as e:
            print(f"Error moving {file}: {e}")

current_dir = os.getcwd()
folder_path = os.path.join(current_dir, "input")

# Create necessary directories for the project
output_dir = os.path.join(current_dir, "output")
output_db_dir = os.path.join(output_dir, "DB")

print("Creating necessary directories...")
os.makedirs(output_dir, exist_ok=True)
os.makedirs(output_db_dir, exist_ok=True)
print(f"Created output directory: {output_dir}")
print(f"Created output/DB directory: {output_db_dir}")

print(f"Starting file cleanup in: {folder_path}")
print(f"Backup folder will be: {os.path.join(folder_path, date_time_folder)}")
delete_csv_and_xlsx_files(folder_path)
print("File cleanup completed successfully")