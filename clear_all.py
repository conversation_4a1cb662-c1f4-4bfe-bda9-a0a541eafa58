import os
import glob
import datetime

# DATE TIME folder name
date_time_folder = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
def delete_csv_and_xlsx_files(folder_path):
    # create datetime folder if it doesn't exist
    if not os.path.exists(os.path.join(folder_path, date_time_folder)):
        os.mkdir(os.path.join(folder_path, date_time_folder))
    # move all csv and xlsx files to datetime folder
    for file in glob.glob(os.path.join(folder_path, "*.csv")):
        os.rename(file, os.path.join(folder_path, date_time_folder, os.path.basename(file)))
    for file in glob.glob(os.path.join(folder_path, "*.xlsx")):
        os.rename(file, os.path.join(folder_path, date_time_folder, os.path.basename(file)))

current_dir = os.getcwd()
# folder_path = "/Users/<USER>/code/cloud"
folder_path = os.path.join(current_dir, "input")
delete_csv_and_xlsx_files(folder_path)


# import os
# import glob
# import pathlib

# def delete_csv_and_xlsx_files(folder_path):
#     # create a text file to store the names of deleted files
#     with open('deleted_files.txt', 'w') as f:
#         # delete all csv and xlsx files and write their names to the text file
#         for file in glob.glob(os.path.join(folder_path, "*.csv")):
#             f.write(file + '\n')
#             os.remove(file)
#         for file in glob.glob(os.path.join(folder_path, "*.xlsx")):
#             f.write(file + '\n')
#             os.remove(file)

# current_dir = os.getcwd()
# folder_path = os.path.join(current_dir, "input")
# delete_csv_and_xlsx_files(folder_path)