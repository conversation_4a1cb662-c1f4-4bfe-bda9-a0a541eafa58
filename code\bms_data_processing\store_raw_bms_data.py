import os
import polars as pl
# Corrected import path assuming this script is in code/bms_data_processing
# and pg_db_utils is in code/db_operations
from ..db_operations.pg_db_utils import get_db_connection, logger, BMS_DATA_COLUMNS

def store_single_csv_to_raw_table(csv_file_path, table_name="bms_charging_data_raw"):
    """
    Reads data from a single CSV file, processes it (filters null timestamps, selects columns),
    and uploads it to the specified raw BMS data table.

    Args:
        csv_file_path (str): The full path to the CSV file.
        table_name (str): The name of the database table to upload data to.
    """
    conn = None
    try:
        logger.info(f"Processing CSV file for raw table: {csv_file_path}")

        # Read CSV using Polars
        try:
            # Infer schema to handle various data types, try_parse_dates for ts_bms
            df = pl.read_csv(csv_file_path, infer_schema_length=10000, try_parse_dates=True)
            logger.info(f"Successfully read {len(df)} rows from {os.path.basename(csv_file_path)}")
        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file_path} with Polars: {e}")
            return

        # Ensure 'ts_bms' column exists
        if "ts_bms" not in df.columns:
            logger.warning(f"'ts_bms' column not found in {os.path.basename(csv_file_path)}. Skipping file for raw table.")
            return
        
        # Data Cleansing: Drop rows where ts_bms is null
        rows_before_cleaning = len(df)
        df = df.filter(pl.col("ts_bms").is_not_null())
        rows_after_cleaning = len(df)
        logger.info(f"Removed {rows_before_cleaning - rows_after_cleaning} rows with null 'ts_bms' from {os.path.basename(csv_file_path)}.")

        if rows_after_cleaning == 0:
            logger.info(f"No data left in {os.path.basename(csv_file_path)} after 'ts_bms' null filter. Skipping upload.")
            return

        # Select and reorder columns to match BMS_DATA_COLUMNS
        # Handle missing columns by adding them with null values if necessary
        current_csv_columns = df.columns
        columns_to_select = []
        for col_name in BMS_DATA_COLUMNS:
            if col_name in current_csv_columns:
                columns_to_select.append(pl.col(col_name))
            else:
                # For the raw table, we might be more lenient or choose to log and skip,
                # but for consistency with the other upload script, we'll add as null.
                logger.warning(f"Column '{col_name}' not found in CSV {os.path.basename(csv_file_path)} for raw table. Will be inserted as NULL.")
                columns_to_select.append(pl.lit(None).alias(col_name))
        
        df_processed = df.select(columns_to_select)
        
        # Convert Polars DataFrame to list of tuples for psycopg2 executemany
        data_to_insert = df_processed.rows()

        if not data_to_insert:
            logger.info(f"No data to insert for {os.path.basename(csv_file_path)} into raw table after processing.")
            return

        conn = get_db_connection()
        if conn is None:
            logger.error(f"Failed to get database connection. Upload to raw table aborted for {os.path.basename(csv_file_path)}.")
            return

        cursor = conn.cursor()

        # Prepare SQL INSERT statement
        columns_str = ", ".join(BMS_DATA_COLUMNS)
        placeholders = ", ".join(["%s"] * len(BMS_DATA_COLUMNS))
        insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

        logger.info(f"Attempting to insert {len(data_to_insert)} rows into raw table '{table_name}' from {os.path.basename(csv_file_path)}...")
        cursor.executemany(insert_query, data_to_insert)
        conn.commit()
        logger.info(f"Successfully inserted {len(data_to_insert)} rows into raw table '{table_name}' from {os.path.basename(csv_file_path)}.")

    except Exception as e:
        logger.error(f"Error processing or uploading data from {csv_file_path} to raw table: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            if 'cursor' in locals() and cursor:
                cursor.close()
            conn.close()
            logger.debug(f"Database connection closed for raw table processing of {os.path.basename(csv_file_path)}.")


def store_all_csv_to_raw_table(source_directory_name="input"):
    """
    Scans the specified source directory (relative to project root) for CSV files
    and processes each one for upload to the bms_charging_data_raw table.
    Args:
        source_directory_name (str): The name of the directory containing CSV files (e.g., "input").
    """
    # Project root is two levels up from code/bms_data_processing/
    project_root = os.path.join(os.path.dirname(__file__), '..', '..')
    abs_source_directory = os.path.join(project_root, source_directory_name)
    
    logger.info(f"Scanning directory for CSV files to load into raw table: {abs_source_directory}")

    if not os.path.isdir(abs_source_directory):
        logger.error(f"Source directory not found: {abs_source_directory}")
        return

    csv_files_found = 0
    for item in os.listdir(abs_source_directory):
        item_path = os.path.join(abs_source_directory, item)
        if os.path.isfile(item_path) and item.lower().endswith(".csv"):
            csv_files_found += 1
            store_single_csv_to_raw_table(item_path) # Default table is bms_charging_data_raw
    
    if csv_files_found == 0:
        logger.info(f"No CSV files found in {abs_source_directory}.")
    else:
        logger.info(f"Finished processing all CSV files in {abs_source_directory} for raw table.")

if __name__ == '__main__':
    # logger.info("Executing script to store BMS data from CSV files into 'bms_charging_data_raw' table...")
    # # Ensure the raw table exists by calling the creation script function
    # # This is a good practice before attempting to insert data.
    # try:
    #     from ..db_operations.pg_create_bms_table import create_bms_charging_data_raw_table
    #     logger.info("Ensuring 'bms_charging_data_raw' table exists...")
    #     create_bms_charging_data_raw_table()
    # except ImportError:
    #     logger.error("Could not import create_bms_charging_data_raw_table. Make sure it's available.")
    # except Exception as e:
    #     logger.error(f"Error ensuring 'bms_charging_data_raw' table exists: {e}")

    # # Create a dummy CSV in 'input/' for testing if it doesn't exist
    # test_csv_dir_main_input = os.path.join(os.path.dirname(__file__), '..', '..', 'input')
    # os.makedirs(test_csv_dir_main_input, exist_ok=True)
    # test_csv_path_main_input = os.path.join(test_csv_dir_main_input, 'sample_raw_bms_data.csv')

    # if not os.path.exists(test_csv_path_main_input):
    #     logger.info(f"Creating a dummy CSV for raw data testing: {test_csv_path_main_input}")
    #     sample_data = {
    #         "bms_vehicle_identification_number": ["VIN_RAW_789", "VIN_RAW_101"],
    #         "bms_scooter_state": ["PARKED", "MOVING"],
    #         "ts_bms": ["2023-02-01T12:00:00", None], # One valid, one null timestamp
    #         "battery_current": [0.5, 15.1],
    #         "pack_soc": [75.0, 60.2],
    #         "some_other_column": ["valueA", "valueB"] # Column not in BMS_DATA_COLUMNS
    #     }
    #     # Ensure all BMS_DATA_COLUMNS are present, fill with None if not in sample_data
    #     for col in BMS_DATA_COLUMNS:
    #         if col not in sample_data:
    #              # Ensure all lists have the same length
    #             sample_data[col] = [None] * len(sample_data[next(iter(sample_data))])


    #     df_sample_raw = pl.DataFrame(sample_data)
    #     df_sample_raw.write_csv(test_csv_path_main_input)
    #     logger.info(f"Dummy CSV for raw data created: {test_csv_path_main_input}")

    store_all_csv_to_raw_table(source_directory_name="input")
    logger.info("Script execution for storing raw BMS data finished.")