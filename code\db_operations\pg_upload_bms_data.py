import os
import polars as pl
from .pg_db_utils import get_db_connection, logger, BMS_DATA_COLUMNS


def upload_bms_data_from_csv(csv_file_path, table_name="bms_charging_data"):
    """
    Reads data from a single CSV file, processes it, and uploads it to the specified table.
    Args:
        csv_file_path (str): The full path to the CSV file.
        table_name (str): The name of the database table to upload data to.
    """
    conn = None
    try:
        logger.info(f"Processing CSV file: {csv_file_path}")

        # Read CSV using Polars
        try:
            df = pl.read_csv(csv_file_path, try_parse_dates=True)
            logger.info(f"Successfully read {len(df)} rows from {os.path.basename(csv_file_path)}")
        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file_path} with Polars: {e}")
            return

        # Data Cleansing: Drop rows where ts_bms is null
        if "ts_bms" not in df.columns:
            logger.error(f"'ts_bms' column not found in {os.path.basename(csv_file_path)}. Skipping file.")
            return
        
        rows_before_cleaning = len(df)
        df = df.filter(pl.col("ts_bms").is_not_null())
        rows_after_cleaning = len(df)
        logger.info(f"Removed {rows_before_cleaning - rows_after_cleaning} rows with null 'ts_bms'.")

        if rows_after_cleaning == 0:
            logger.info(f"No data left in {os.path.basename(csv_file_path)} after cleaning. Skipping upload.")
            return

        # Select and reorder columns to match BMS_DATA_COLUMNS
        # Handle missing columns by adding them with null values if necessary
        current_csv_columns = df.columns
        columns_to_select = []
        for col_name in BMS_DATA_COLUMNS:
            if col_name in current_csv_columns:
                columns_to_select.append(pl.col(col_name))
            else:
                logger.warning(f"Column '{col_name}' not found in CSV {os.path.basename(csv_file_path)}. Will be inserted as NULL.")
                columns_to_select.append(pl.lit(None).alias(col_name)) # Add as null literal
        
        df_processed = df.select(columns_to_select)

        # Convert Polars DataFrame to list of tuples for psycopg2 executemany
        # Handle potential type issues or NaNs if necessary before conversion
        # Polars handles NaNs appropriately for database insertion in many cases
        data_to_insert = df_processed.rows()

        if not data_to_insert:
            logger.info(f"No data to insert for {os.path.basename(csv_file_path)} after processing.")
            return

        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection. Upload aborted for this file.")
            return

        cursor = conn.cursor()

        # Prepare SQL INSERT statement
        columns_str = ", ".join(BMS_DATA_COLUMNS)
        placeholders = ", ".join(["%s"] * len(BMS_DATA_COLUMNS))
        insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

        logger.info(f"Attempting to insert {len(data_to_insert)} rows into '{table_name}' from {os.path.basename(csv_file_path)}...")
        cursor.executemany(insert_query, data_to_insert)
        conn.commit()
        logger.info(f"Successfully inserted {len(data_to_insert)} rows into '{table_name}' from {os.path.basename(csv_file_path)}.")

    except Exception as e:
        logger.error(f"Error processing or uploading data from {csv_file_path}: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            if 'cursor' in locals() and cursor:
                cursor.close()
            conn.close()
            logger.debug(f"Database connection closed for {os.path.basename(csv_file_path)} processing.")


def process_all_csv_in_directory(directory_path="input/bms_csv_data"):
    """
    Scans a directory for CSV files and processes each one for upload.
    Args:
        directory_path (str): The path to the directory containing CSV files.
                               Relative to the project root.
    """
    # Construct absolute path for the directory
    # Assumes this script is in code/db_operations/
    project_root = os.path.join(os.path.dirname(__file__), '..', '..')
    abs_directory_path = os.path.join(project_root, directory_path)
    
    logger.info(f"Scanning directory for CSV files: {abs_directory_path}")

    if not os.path.isdir(abs_directory_path):
        logger.error(f"Directory not found: {abs_directory_path}")
        return

    csv_files_found = 0
    for filename in os.listdir(abs_directory_path):
        if filename.lower().endswith(".csv"):
            csv_files_found += 1
            file_path = os.path.join(abs_directory_path, filename)
            upload_bms_data_from_csv(file_path)
    
    if csv_files_found == 0:
        logger.info(f"No CSV files found in {abs_directory_path}.")
    else:
        logger.info(f"Finished processing all CSV files in {abs_directory_path}.")


if __name__ == '__main__':
    logger.info("Executing script to upload BMS data from CSV files...")
    # Create dummy CSV for testing if it doesn't exist
    test_csv_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'input', 'bms_csv_data')
    os.makedirs(test_csv_dir, exist_ok=True)
    test_csv_path = os.path.join(test_csv_dir, 'sample_bms_data.csv')

    if not os.path.exists(test_csv_path):
        logger.info(f"Creating a dummy CSV for testing: {test_csv_path}")
        # Create a Polars DataFrame with some sample data matching some EXPECTED_COLUMNS
        sample_data = {
            "bms_vehicle_identification_number": ["VIN123", "VIN456"],
            "bms_scooter_state": ["CHARGING", "IDLE"],
            "kwhr_charged_discharged_mode": [1.23, None], # Example with None
            "ts_bms": ["2023-01-01T10:00:00", "2023-01-01T11:00:00"],
            "battery_current": [10.5, -2.1],
            "pack_soc": [85.5, 90.1],
            # Add a column not in BMS_DATA_COLUMNS to test selection
            "extra_column_not_for_db": ["extra1", "extra2"]
        }
        # Ensure all BMS_DATA_COLUMNS are present, fill with None if not in sample_data
        for col in BMS_DATA_COLUMNS:
            if col not in sample_data:
                sample_data[col] = [None] * len(sample_data[next(iter(sample_data))]) # Fill with Nones

        df_sample = pl.DataFrame(sample_data)
        df_sample.write_csv(test_csv_path)
        logger.info(f"Dummy CSV created: {test_csv_path}")

    process_all_csv_in_directory()
    logger.info("Script execution finished.")