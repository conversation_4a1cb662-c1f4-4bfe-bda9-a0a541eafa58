# -*- coding: utf-8 -*-
'''
>>> import os
>>> import random
>>> import sys
>>> from art import *
>>> import string
>>> lprint(length=10, height=1, char="#")
##########
>>> lprint(length=15, height=2, char="*")
***************
***************
>>> lprint(length=0, height=1, char="#")
Traceback (most recent call last):
        ...
art.art.artError: The 'length' must be an int higher than 0.
>>> lprint(length=15, height='test', char="#")
Traceback (most recent call last):
        ...
art.art.artError: The 'height' must be an int higher than 0.
>>> lprint(length=15, height=2, char=4)
Traceback (most recent call last):
        ...
art.art.artError: The 'char' type must be str.
>>> line(length=10, height=1, char="#")
'##########'
>>> line(length=15, height=2, char="*")
'***************\n***************'
>>> line(length=0, height=1, char="#")
Traceback (most recent call last):
        ...
art.art.artError: The 'length' must be an int higher than 0.
>>> line(length=15, height='test', char="#")
Traceback (most recent call last):
        ...
art.art.artError: The 'height' must be an int higher than 0.
>>> line(length=15, height=2, char=4)
Traceback (most recent call last):
        ...
art.art.artError: The 'char' type must be str.
>>> tprint("\t\t2","block")
<BLANKLINE>
 .----------------.
| .--------------. |
| |    _____     | |
| |   / ___ `.   | |
| |  |_/___) |   | |
| |   .'____.'   | |
| |  / /____     | |
| |  |_______|   | |
| |              | |
| '--------------' |
 '----------------'
<BLANKLINE>
>>> tprint("\t\t2","block",sep=2)
<BLANKLINE>
 .----------------.
| .--------------. |
| |    _____     | |
| |   / ___ `.   | |
| |  |_/___) |   | |
| |   .'____.'   | |
| |  / /____     | |
| |  |_______|   | |
| |              | |
| '--------------' |
 '----------------'
<BLANKLINE>
>>> tprint("\t\t2","block",sep="\n\n")
<BLANKLINE>
<BLANKLINE>
 .----------------.
<BLANKLINE>
| .--------------. |
<BLANKLINE>
| |    _____     | |
<BLANKLINE>
| |   / ___ `.   | |
<BLANKLINE>
| |  |_/___) |   | |
<BLANKLINE>
| |   .'____.'   | |
<BLANKLINE>
| |  / /____     | |
<BLANKLINE>
| |  |_______|   | |
<BLANKLINE>
| |              | |
<BLANKLINE>
| '--------------' |
<BLANKLINE>
 '----------------'
<BLANKLINE>
<BLANKLINE>
>>> tprint(" ","block")
<BLANKLINE>
>>> tprint("123","alpha")
<BLANKLINE>
>>> tprint('HEY')
 _   _  _____ __   __
| | | || ____|\ \ / /
| |_| ||  _|   \ V /
|  _  || |___   | |
|_| |_||_____|  |_|
<BLANKLINE>
<BLANKLINE>
>>> tprint('HEY', space=6)
 _   _        _____       __   __
| | | |      | ____|      \ \ / /
| |_| |      |  _|         \ V /
|  _  |      | |___         | |
|_| |_|      |_____|        |_|
<BLANKLINE>
<BLANKLINE>
>>> font_list(mode="ascii")
1943 :
#### ##  ### ###   ## ##   #### ##
# ## ##   ##  ##  ##   ##  # ## ##
  ##      ##      ####       ##
  ##      ## ##    #####     ##
  ##      ##          ###    ##
  ##      ##  ##  ##   ##    ##
 ####    ### ###   ## ##    ####
<BLANKLINE>
<BLANKLINE>
1row :
~|~ [- _\~ ~|~
<BLANKLINE>
<BLANKLINE>
3-d :
   **                     **
  /**                    /**
 ******  *****   ****** ******
///**/  **///** **//// ///**/
  /**  /*******//*****   /**
  /**  /**////  /////**  /**
  //** //****** ******   //**
   //   ////// //////     //
<BLANKLINE>
3d_diagonal :
<BLANKLINE>
<BLANKLINE>
    ___                                 ___
  ,--.'|_                             ,--.'|_
  |  | :,'                            |  | :,'
  :  : ' :               .--.--.      :  : ' :
.;__,'  /      ,---.    /  /    '   .;__,'  /
|  |   |      /     \  |  :  /`./   |  |   |
:__,'| :     /    /  | |  :  ;_     :__,'| :
  '  : |__  .    ' / |  \  \    `.    '  : |__
  |  | '.'| '   ;   /|   `----.   \   |  | '.'|
  ;  :    ; '   |  / |  /  /`--'  /   ;  :    ;
  |  ,   /  |   :    | '--'.     /    |  ,   /
   ---`-'    \   \  /    `--'---'      ---`-'
              `----'
<BLANKLINE>
<BLANKLINE>
3x5 :
<BLANKLINE>
 #           #
### ###  ## ###
 #  ##   #   #
 ## ### ##   ##
<BLANKLINE>
<BLANKLINE>
4max :
888888 888888 .dP"Y8 888888
  88   88__   `Ybo."   88
  88   88""   o.`Y8b   88
  88   888888 8bodP'   88
<BLANKLINE>
4x4_offr :
 ######   ######    ####    ######
   ##     ##       ##  ##     ##
   ##     ##       ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     ##       ##  ##     ##
   ##     ######    ####      ##
<BLANKLINE>
<BLANKLINE>
5lineoblique :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 __  ___     ___        ___     __  ___
  / /      //___) )   ((   ) )   / /
 / /      //           \ \      / /
/ /      ((____     //   ) )   / /
<BLANKLINE>
5x7 :
 #                  #
 #                  #
###    ##    ###   ###
 #    # ##  ##      #
 #    ##      ##    #
  ##   ##   ###      ##
<BLANKLINE>
<BLANKLINE>
5x8 :
   #     #   ###     #
 # #     #         # #
  #      #          #
         #
       ###     #
 #                 #
 #             #   #
 #  #          #   #  #
<BLANKLINE>
64f1 :
#######  ####      #####   #######
#######  ##       ###      #######
#######  #####     #####   #######
  ###    ##           ###    ###
  ###    ##           ###    ###
  ###    #######  #######    ###
  ###    #######  #######    ###
  ###    #######  ######     ###
<BLANKLINE>
6x10 :
<BLANKLINE>
  #                    #
  #                    #
 ####    ###    ###   ####
  #     #   #  #       #
  #     #####   ###    #
  #  #  #          #   #  #
   ##    ###   ####     ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
6x9 :
  #                    #
  #                    #
 ###                  ###
  #      ##    ####    #
  # #   # ##   ##      # #
  # #   ##       ##    # #
   #     ###   ####     #
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
a_zooloo :
######   ######    ######  ######
 ######   ######  ######    ######
  ###     ##      ##         ###
  ###    ######   #####      ###
  ###    #####     #####     ###
  ###    ###          ###    ###
  ###    #######   ######    ###
 #####    #####   ######    #####
<BLANKLINE>
acrobatic :
  o                               o
 <|>                             <|>
 < >                             < >
  |        o__  __o       __o__   |
  o__/_   /v      |>     />  \    o__/_
  |      />      //      \o       |
  |      \o    o/         v\      |
  o       v\  /v __o       <\     o
  <\__     <\/> __/>  _\o__</     <\__
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
advenger :
 # ##### #######   #####    # #####
## ## ##  ##   #  ##   ##  ## ## ##
   ##     ##      ##          ##
   ##     ####     #####      ##
   ##     ##           ##     ##
   ##     ##   #  ##   ##     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
alligator :
  :::::::::::       ::::::::::       ::::::::   :::::::::::
     :+:           :+:             :+:    :+:      :+:
    +:+           +:+             +:+             +:+
   +#+           +#++:++#        +#++:++#++      +#+
  +#+           +#+                    +#+      +#+
 #+#           #+#             #+#    #+#      #+#
###           ##########       ########       ###
<BLANKLINE>
alligator2 :
::::::::::: ::::::::::  ::::::::  :::::::::::
    :+:     :+:        :+:    :+:     :+:
    +:+     +:+        +:+            +:+
    +#+     +#++:++#   +#++:++#++     +#+
    +#+     +#+               +#+     +#+
    #+#     #+#        #+#    #+#     #+#
    ###     ##########  ########      ###
<BLANKLINE>
alligator3 :
::::::::::: ::::::::::  ::::::::  :::::::::::
    :+:     :+:        :+:    :+:     :+:
    +:+     +:+        +:+            +:+
    +#+     +#++:++#   +#++:++#++     +#+
    +#+     +#+               +#+     +#+
    #+#     #+#        #+#    #+#     #+#
    ###     ##########  ########      ###
<BLANKLINE>
alpha :
      _____                    _____                    _____                _____
     /\    \                  /\    \                  /\    \              /\    \
    /::\    \                /::\    \                /::\    \            /::\    \
    \:::\    \              /::::\    \              /::::\    \           \:::\    \
     \:::\    \            /::::::\    \            /::::::\    \           \:::\    \
      \:::\    \          /:::/\:::\    \          /:::/\:::\    \           \:::\    \
       \:::\    \        /:::/__\:::\    \        /:::/__\:::\    \           \:::\    \
       /::::\    \      /::::\   \:::\    \       \:::\   \:::\    \          /::::\    \
      /::::::\    \    /::::::\   \:::\    \    ___\:::\   \:::\    \        /::::::\    \
     /:::/\:::\    \  /:::/\:::\   \:::\    \  /\   \:::\   \:::\    \      /:::/\:::\    \
    /:::/  \:::\____\/:::/__\:::\   \:::\____\/::\   \:::\   \:::\____\    /:::/  \:::\____\
   /:::/    \::/    /\:::\   \:::\   \::/    /\:::\   \:::\   \::/    /   /:::/    \::/    /
  /:::/    / \/____/  \:::\   \:::\   \/____/  \:::\   \:::\   \/____/   /:::/    / \/____/
 /:::/    /            \:::\   \:::\    \       \:::\   \:::\    \      /:::/    /
/:::/    /              \:::\   \:::\____\       \:::\   \:::\____\    /:::/    /
\::/    /                \:::\   \::/    /        \:::\  /:::/    /    \::/    /
 \/____/                  \:::\   \/____/          \:::\/:::/    /      \/____/
                           \:::\    \               \::::::/    /
                            \:::\____\               \::::/    /
                             \::/    /                \::/    /
                              \/____/                  \/____/
<BLANKLINE>
<BLANKLINE>
alphabet :
 t           t
 t           t
ttt eee  ss ttt
 t  e e  s   t
 tt ee  ss   tt
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
amc3line :
.-. .-. .-. .-.
 |  |-  `-.  |
 '  `-' `-'  '
<BLANKLINE>
<BLANKLINE>
amc3liv1 :
.               .: S;:. .
S:;s;:' S  S  S S  S  S S:;s;:'
`       `:;S;:' `:;S :' `
<BLANKLINE>
<BLANKLINE>
amcaaa01 :
sdSS_SSSSSSbs    sSSs    sSSs  sdSS_SSSSSSbs
YSSS~S%SSSSSP   d%%SP   d%%SP  YSSS~S%SSSSSP
     S%S       d%S'    d%S'         S%S
     S%S       S%S     S%|          S%S
     S&S       S&S     S&S          S&S
     S&S       S&S_Ss  Y&Ss         S&S
     S&S       S&S~SP  `S&&S        S&S
     S&S       S&S       `S*S       S&S
     S*S       S*b        l*S       S*S
     S*S       S*S.      .S*P       S*S
     S*S        SSSbs  sSS*S        S*S
     S*S         YSSP  YSS'         S*S
     SP                             SP
     Y                              Y
<BLANKLINE>
<BLANKLINE>
amcneko :
<BLANKLINE>
.sSSSSSSSSSSSSSs. .sSSSSs.    .sSSSSSSSs. .sSSSSSSSSSSSSSs.
SSSSS S SSS SSSSS S SSSSSSSs. S SSS SSSS' SSSSS S SSS SSSSS
SSSSS S  SS SSSSS S  SS SSSS' S  SS       SSSSS S  SS SSSSS
`:S:' S..SS `:S:' S..SS       `SSSSsSSSa. `:S:' S..SS `:S:'
      S:::S       S:::SSSS    .sSSS SSSSS       S:::S
      S;;;S       S;;;S       S;;;S SSSSS       S;;;S
      S%%%S       S%%%S SSSSS S%%%S SSSSS       S%%%S
      SSSSS       SSSSSsSS;:' SSSSSsSSSSS       SSSSS
<BLANKLINE>
<BLANKLINE>
amcrazo2 :
   . .    .       . .       . .       . .    .
.+'|=|`+.=|`+. .+'|=|`+. .+'|=|`+. .+'|=|`+.=|`+.
|.+' |  | `+.| |  | `+.| |  | `+.| |.+' |  | `+.|
     |  |      |  |=|`.  |  | .         |  |
     |  |      |  | `.|  `+.|=|`+.      |  |
     |  |      |  |    . .    |  |      |  |
     |  |      |  | .+'| |`+. |  |      |  |
     |.+'      `+.|=|.+' `+.|=|.+'      |.+'
<BLANKLINE>
<BLANKLINE>
amcrazor :
 ___  ___   ___        ___  ___   ___  ___  ___   ___
`._|=|   |=|_.'   .'|=|_.' |   |=|_.' `._|=|   |=|_.'
     |   |      .'  |  ___ `.  |           |   |
     |   |      |   |=|_.'   `.|=|`.       |   |
     `.  |      |   |  ___  ___  |  `.     `.  |
       `.|      |___|=|_.'  `._|=|___|       `.|
<BLANKLINE>
<BLANKLINE>
amcslash :
<BLANKLINE>
.s5SSSSs. .s5SSSs.  .s5SSSs.  .s5SSSSs.
   SSS          SS.       SS.    SSS
   S%S    sS    `:; sS    `:;    S%S
   S%S    SSSs.     `:;;;;.      S%S
   S%S    SS              ;;.    S%S
   `:;    SS              `:;    `:;
   ;,.    SS    ;,. .,;   ;,.    ;,.
   ;:'    `:;;;;;:' `:;;;;;:'    ;:'
<BLANKLINE>
<BLANKLINE>
amcthin :
.-..-..-. .-..--. .-..-. .-..-..-.
 ~ | | ~  | | ~~  | | ~   ~ | | ~
   | |    | | _    \|       | |
   | |    | |`-'     |\     | |
   | |    | | __   _ | |    | |
   `-'    `-'`--' `-'`-'    `-'
<BLANKLINE>
<BLANKLINE>
amctubes :
sss sssss d sss     sss. sss sssss
    S     S       d          S
    S     S       Y          S
    S     S sSSs    ss.      S
    S     S            b     S
    S     S            P     S
    P     P sSSss ` ss'      P
<BLANKLINE>
<BLANKLINE>
amcun1 :
<BLANKLINE>
<BLANKLINE>
,'',,'',,'', ,'',,'', ,'',,'', ,'',,'',,'',
',,';  ;',,' ;  ;',,' ;  ;',,' ',,';  ;',,'
    ;  ;     ;  ;',,' ',,','',     ;  ;
    ;  ;     ;  ; ,,  ,'',;  ;     ;  ;
    ',,'     ',,'',,' ',,'',,'     ',,'
<BLANKLINE>
<BLANKLINE>
aquaplan :
 ######   ######    ####    ######
   ##     ##       ##  ##     ##
   ##     ##       ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     ##       ##  ##     ##
   ##     ######    ####      ##
<BLANKLINE>
<BLANKLINE>
arrows :
  >=>                       >=>
  >=>                       >=>
>=>>==>   >==>     >===>  >=>>==>
  >=>   >>   >=>  >=>       >=>
  >=>   >>===>>=>   >==>    >=>
  >=>   >>            >=>   >=>
   >=>   >====>   >=> >=>    >=>
<BLANKLINE>
<BLANKLINE>
asc :
<BLANKLINE>
<BLANKLINE>
 #######  #######  #######  #######
    #     #        #           #
    #     ####     #######     #
    #     #              #     #
    #     #######  #######     #
<BLANKLINE>
<BLANKLINE>
ascii :
 ######   #######   #####   ######
 # ## #    ##   #  ##   ##  # ## #
   ##      ## #    #          ##
   ##      ####     #####     ##
   ##      ## #         ##    ##
   ##      ##   #  ##   ##    ##
  ####    #######   #####    ####
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
ascii_new_roman :
 ____,  ____,  ____,  ____,
(-|    (-|_,  (-(__  (-|
 _|,    _|__,  ____)  _|,
(      (      (      (
<BLANKLINE>
assalt_m :
 #######  #######  #######  #######
   ###    ##       ##         ###
   ###    ##       #######    ###
   ###    #######       ##    ###
   ###    ###      ###  ##    ###
   ###    ###      ###  ##    ###
   ###    #######  #######    ###
<BLANKLINE>
<BLANKLINE>
asslt_m :
 #######  #######  #######  #######
   ###    ##       ##         ###
   ###    ##       #######    ###
   ###    #######       ##    ###
   ###    ###      ###  ##    ###
   ###    # #      ###  ##    ###
   ###    #######  #######    ###
<BLANKLINE>
<BLANKLINE>
atc :
#        #        #        #
#  # #   #   ##   #   #    #  # #
###   ## #   #### #   #### ###   ##
###   ## #     ## #        ###   ##
###   ## #   #### #####    ###   ##
###   ## #   ##   #   #    ###   ##
###   ## #        #        ###   ##
######## ######## ######## ########
<BLANKLINE>
atc_gran :
#        #        #        #
#  # #   #   ##   #   #    #  # #
###   ## #   #### #   #### ###   ##
###   ## #     ## #        ###   ##
###   ## #   #### #####    ###   ##
###   ## #   ##   #   #    ###   ##
###   ## #        #        ###   ##
######## ######## ######## ########
<BLANKLINE>
avatar :
 _____  _____ ____  _____
/__ __\/  __// ___\/__ __\
  / \  |  \  |    \  / \
  | |  |  /_ \___ |  | |
  \_/  \____\\____/  \_/
<BLANKLINE>
<BLANKLINE>
b1ff :
T35T
<BLANKLINE>
banner :
<BLANKLINE>
##### ######  ####  #####
  #   #      #        #
  #   #####   ####    #
  #   #           #   #
  #   #      #    #   #
  #   ######  ####    #
<BLANKLINE>
<BLANKLINE>
banner3 :
######## ########  ######  ########
   ##    ##       ##    ##    ##
   ##    ##       ##          ##
   ##    ######    ######     ##
   ##    ##             ##    ##
   ##    ##       ##    ##    ##
   ##    ########  ######     ##
<BLANKLINE>
banner3-d :
'########:'########::'######::'########:
... ##..:: ##.....::'##... ##:... ##..::
::: ##:::: ##::::::: ##:::..::::: ##::::
::: ##:::: ######:::. ######::::: ##::::
::: ##:::: ##...:::::..... ##:::: ##::::
::: ##:::: ##:::::::'##::: ##:::: ##::::
::: ##:::: ########:. ######::::: ##::::
:::..:::::........:::......::::::..:::::
<BLANKLINE>
banner4 :
.########.########..######..########
....##....##.......##....##....##...
....##....##.......##..........##...
....##....######....######.....##...
....##....##.............##....##...
....##....##.......##....##....##...
....##....########..######.....##...
<BLANKLINE>
barbwire :
  ><<                     ><<
  ><<                     ><<
><>< ><   ><<     ><<<< ><>< ><
  ><<   ><   ><< ><<      ><<
  ><<  ><<<<< ><<  ><<<   ><<
  ><<  ><            ><<  ><<
   ><<   ><<<<   ><< ><<   ><<
<BLANKLINE>
<BLANKLINE>
basic :
d888888b d88888b .d8888. d888888b
`~~88~~' 88'     88'  YP `~~88~~'
   88    88ooooo `8bo.      88
   88    88~~~~~   `Y8b.    88
   88    88.     db   8D    88
   YP    Y88888P `8888Y'    YP
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
battlesh :
######  #######   ######  ######
######  #######  #######  ######
  ##     ##  ##  ##   ##    ##
  ##     ####     ###       ##
  ##     ####       ###     ##
  ##     ##  ##  ##   ##    ##
 ####   #######  #######   ####
 ####   #######  ######    ####
<BLANKLINE>
baz_bil :
######   ######    ####    ######
  ##     ##       ##   #     ##
  ##     ##       ##         ##
  ##     ####      ####      ##
  ##     ##           ##     ##
  ##     ##           ##     ##
  ##     ##       #   ##     ##
  ##     ######    ####      ##
<BLANKLINE>
bear :
   _     _      _     _      _     _      _     _
  (c).-.(c)    (c).-.(c)    (c).-.(c)    (c).-.(c)
   / ._. \      / ._. \      / ._. \      / ._. \
 __\( Y )/__  __\( Y )/__  __\( Y )/__  __\( Y )/__
(_.-/'-'\-._)(_.-/'-'\-._)(_.-/'-'\-._)(_.-/'-'\-._)
   || T ||      || E ||      || S ||      || T ||
 _.' `-' '._  _.' `-' '._  _.' `-' '._  _.' `-' '._
(.-./`-'\.-.)(.-./`-'\.-.)(.-./`-`\.-.)(.-./`-'\.-.)
 `-'     `-'  `-'     `-'  `-'     `-'  `-'     `-'
<BLANKLINE>
beer_pub :
 ######  #######   #####    ######
 # ## #  ### ###  ###  ##   # ## #
   ##    ###      ###         ##
   ##    #####     #####      ##
   ##    ###           ##     ##
   ##    ### ###  ###  ##     ##
   ##    #######   #####      ##
<BLANKLINE>
<BLANKLINE>
bell :
  .                   .
 _/_     ___    ____ _/_
  |    .'   `  (      |
  |    |----'  `--.   |
  \__/ `.___, \___.'  \__/
<BLANKLINE>
<BLANKLINE>
benjamin :
"|"[-_\""|"
<BLANKLINE>
big :
 _               _
| |             | |
| |_   ___  ___ | |_
| __| / _ \/ __|| __|
| |_ |  __/\__ \| |_
 \__| \___||___/ \__|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
bigchief :
_________________________
<BLANKLINE>
<BLANKLINE>
--_/_-----__----__---_/_-
  /     /___)  (_ `  /
_(_ ___(___ __(__)__(_ __
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
bigfig :
<BLANKLINE>
_|_ _  _ _|_
 |_(/__>  |_
<BLANKLINE>
binary :
01110100 01100101 01110011 01110100
<BLANKLINE>
block :
<BLANKLINE>
 .----------------.  .----------------.  .----------------.  .----------------.
| .--------------. || .--------------. || .--------------. || .--------------. |
| |  _________   | || |  _________   | || |    _______   | || |  _________   | |
| | |  _   _  |  | || | |_   ___  |  | || |   /  ___  |  | || | |  _   _  |  | |
| | |_/ | | \_|  | || |   | |_  \_|  | || |  |  (__ \_|  | || | |_/ | | \_|  | |
| |     | |      | || |   |  _|  _   | || |   '.___`-.   | || |     | |      | |
| |    _| |_     | || |  _| |___/ |  | || |  |`\____) |  | || |    _| |_     | |
| |   |_____|    | || | |_________|  | || |  |_______.'  | || |   |_____|    | |
| |              | || |              | || |              | || |              | |
| '--------------' || '--------------' || '--------------' || '--------------' |
 '----------------'  '----------------'  '----------------'  '----------------'
<BLANKLINE>
block2 :
<BLANKLINE>
  _|                            _|
_|_|_|_|    _|_|      _|_|_|  _|_|_|_|
  _|      _|_|_|_|  _|_|        _|
  _|      _|            _|_|    _|
    _|_|    _|_|_|  _|_|_|        _|_|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
bolger :
  d8                      d8
_d88__  e88~~8e   d88~\ _d88__
 888   d888  88b C888    888
 888   8888__888  Y88b   888
 888   Y888    ,   888D  888
 "88_/  "88___/  \_88P   "88_/
<BLANKLINE>
<BLANKLINE>
braced :
.-----. .----.  .----. .-----.
`-' '-' } |__} { {__-` `-' '-'
  } {   } '__} .-._} }   } {
  `-'   `----' `----'    `-'
<BLANKLINE>
<BLANKLINE>
bright :
.######..######...####...######.
...##....##......##........##...
...##....####.....####.....##...
...##....##..........##....##...
...##....######...####.....##...
................................
<BLANKLINE>
broadway :
<BLANKLINE>
8888888 8888888888 8 8888888888      d888888o.   8888888 8888888888
      8 8888       8 8888          .`8888:' `88.       8 8888
      8 8888       8 8888          8.`8888.   Y8       8 8888
      8 8888       8 8888          `8.`8888.           8 8888
      8 8888       8 888888888888   `8.`8888.          8 8888
      8 8888       8 8888            `8.`8888.         8 8888
      8 8888       8 8888             `8.`8888.        8 8888
      8 8888       8 8888         8b   `8.`8888.       8 8888
      8 8888       8 8888         `8b.  ;8.`8888       8 8888
      8 8888       8 888888888888  `Y8888P ,88P'       8 8888
<BLANKLINE>
bubble :
  _    _    _    _
 / \  / \  / \  / \
( t )( e )( s )( t )
 \_/  \_/  \_/  \_/
<BLANKLINE>
bulbhead :
 ____  ____  ___  ____
(_  _)( ___)/ __)(_  _)
  )(   )__) \__ \  )(
 (__) (____)(___/ (__)
<BLANKLINE>
c1 :
######## #######   #####   ########
#  ##  #  ##  ##  ##   ##  #  ##  #
   ##     ##      ##          ##
   ##     ####     #####      ##
   ##     ##           ##     ##
   ##     ##  ##  ##   ##     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
c2 :
<BLANKLINE>
 ######  #######   #####    ######
 ######  #######  #######   ######
<BLANKLINE>
   ##    ####     ######      ##
   ##    ##            ##     ##
   ##    #######  #######     ##
   ##    #######   #####      ##
<BLANKLINE>
c_ascii :
 ######   #######   #####   ######
 # ## #    ##   #  ##   ##  # ## #
   ##      ## #    ##         ##
   ##      ####     #####     ##
   ##      ## #         ##    ##
   ##      ##   #  ##   ##    ##
  ####    #######   #####    ####
<BLANKLINE>
<BLANKLINE>
c_consen :
<BLANKLINE>
<BLANKLINE>
 ######   #####     #####   ######
   ##     ##       ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     #####    #####      ##
<BLANKLINE>
<BLANKLINE>
calgphy2 :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
    #                              #
   ##                             ##
   ##                             ##
 ########    /##       /###     ########
########    / ###     / #### / ########
   ##      /   ###   ##  ###/     ##
   ##     ##    ### ####          ##
   ##     ########    ###         ##
   ##     #######       ###       ##
   ##     ##              ###     ##
   ##     ####    /  /###  ##     ##
   ##      ######/  / #### /      ##
    ##      #####      ###/        ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
caligraphy :
<BLANKLINE>
<BLANKLINE>
    *                              *
   **                             **
   **                             **
 ********              ****     ********
********     ***      * **** * ********
   **       * ***    **  ****     **
   **      *   ***  ****          **
   **     **    ***   ***         **
   **     ********      ***       **
   **     *******         ***     **
   **     **         ****  **     **
   **     ****    * * **** *      **
    **     *******     ****        **
            *****
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
cards :
.------..------..------..------.
|T.--. ||E.--. ||S.--. ||T.--. |
| :/\: || (\/) || :/\: || :/\: |
| (__) || :\/: || :\/: || (__) |
| '--'T|| '--'E|| '--'S|| '--'T|
`------'`------'`------'`------'
<BLANKLINE>
catwalk :
  _//                     _//
  _//                     _//
_/_/ _/   _//     _//// _/_/ _/
  _//   _/   _// _//      _//
  _//  _///// _//  _///   _//
  _//  _/            _//  _//
   _//   _////   _// _//   _//
<BLANKLINE>
<BLANKLINE>
char1 :
<BLANKLINE>
 ######   ######    ####    ######
   ##     ##       ##         ##
   ##     #####     ####      ##
   ##     ##           ##     ##
   ##     ##           ##     ##
   ##     ######    ####      ##
<BLANKLINE>
<BLANKLINE>
char2 :
#####     ######   ######  #####
######   #######  #######  ######
    ##   ##       ##           ##
    ##   #######   #####       ##
    ##   ##            ##      ##
    ##   #######  #######      ##
    ##    ######  ######       ##
<BLANKLINE>
<BLANKLINE>
char3 :
  #####   ######    ####     #####
    #     #        #    #      #
    #     #        #           #
    #     ####      ####       #
    #     #             #      #
    #     #        #    #      #
    #     ######    ####       #
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
char4 :
   ###      ###      ###      ###
#######    #####    #####  #######
  ##      ##       ##        ##
  ##     ####       ####     ##
  ##     ##   ##       ##    ##
  ##     ##  ###  ##   ##    ##
  ##     ######    #####     ##
<BLANKLINE>
<BLANKLINE>
charact1 :
<BLANKLINE>
 ######   ######   ######   ######
 ######   ######   ###      ######
   ##     ##       ######     ##
   ##     ####         ##     ##
   ##     ##       ######     ##
   ##     ######   ######     ##
<BLANKLINE>
<BLANKLINE>
charact2 :
<BLANKLINE>
 ######    #####    #####   ######
   ##     ##       ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     ##           ##     ##
   ##      #####   #####      ##
<BLANKLINE>
<BLANKLINE>
charact3 :
<BLANKLINE>
  ######   ######    ####    ######
    ##    ###  ##   ##  ##     ##
   ##     ####      ###       ##
   ##     ##          ###     ##
  ##     ###  ##  ##   ##    ##
  ##     #######   #####     ##
<BLANKLINE>
<BLANKLINE>
charact4 :
<BLANKLINE>
 ######   ######   ######   ######
 ######   ##       ### ##   ######
   ##     ####     ###        ##
   ##     ###      ######     ##
   ##     ###         ###     ##
   ##     ######   ######     ##
<BLANKLINE>
<BLANKLINE>
charact5 :
<BLANKLINE>
 ######  #######    #####   ######
 ######   ###      ###  #   ######
 # ## #   #####    #####    # ## #
   ##     ###       #####     ##
   ##     ###      #  ###     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
charact6 :
<BLANKLINE>
 ######   ######    #####   ######
   ##     ##       ###        ##
   ##     ####      ###       ##
   ##     ##         ###      ##
   ##     ##          ###     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
characte :
<BLANKLINE>
 ######   ######   ######   ######
   ##     ##       ##  ##     ##
   ##     ####     ###        ##
   ##     ##       ######     ##
   ##     ##           ##     ##
   ##     ######   ######     ##
<BLANKLINE>
<BLANKLINE>
chartr :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
#         #
##  #   # ##
#  ### #  #
#  #   ## #
##  ## #  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
chartri :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 #            #
###   #   ## ###
#    ###  #  #
#   ##     # #
##   ### ##  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
chiseled :
 ,--.--------.       ,----.     ,-,--.   ,--.--------.
/==/,  -   , -\   ,-.--` , \  ,-.'-  _\ /==/,  -   , -\
\==\.-.  - ,-./  |==|-  _.-` /==/_ ,_.' \==\.-.  - ,-./
 `--`\==\- \     |==|   `.-. \==\  \     `--`\==\- \
      \==\_ \   /==/_ ,    /  \==\ -\         \==\_ \
      |==|- |   |==|    .-'   _\==\ ,\        |==|- |
      |==|, |   |==|_  ,`-._ /==/\/ _ |       |==|, |
      /==/ -/   /==/ ,     / \==\ - , /       /==/ -/
      `--`--`   `--`-----``   `--`---'        `--`--`
<BLANKLINE>
chunky :
 __                  __
|  |_ .-----..-----.|  |_
|   _||  -__||__ --||   _|
|____||_____||_____||____|
<BLANKLINE>
<BLANKLINE>
clb6x10 :
  ##                   ##
  ##                   ##
  ##                   ##
 ####    ###    ####  ####
  ##    ## ##  ##      ##
  ##    #####   ###    ##
  ##    ##        ##   ##
   ##    ###   ####     ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
clb8x10 :
   ##                         ##
   ##                         ##
   ##                         ##
 ######    #####    #####   ######
   ##     ##   ##  ##         ##
   ##     #######   ####      ##
   ##     ##           ##     ##
    ###    #####   #####       ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
clb8x8 :
   ##                         ##
   ##                         ##
 ######    #####    #####   ######
   ##     ##   ##  ##         ##
   ##     #######   ####      ##
   ##     ##           ##     ##
    ###    #####   #####       ###
<BLANKLINE>
<BLANKLINE>
cli8x8 :
    ##                         ##
   ##                         ##
#######     ####     ##### #######
  ##      ##   ##   ##       ##
 ##      ########    ##     ##
 ##      ##           ##    ##
  ###     ####    #####      ###
<BLANKLINE>
<BLANKLINE>
clr4x6 :
 #              #
###   ##   ##  ###
 #   ###  ##    #
 #   #     ##   #
 #    ##  ##    #
<BLANKLINE>
<BLANKLINE>
clr5x10 :
<BLANKLINE>
<BLANKLINE>
  #                 #
 ####   ##    ###  ####
  #    #  #  #      #
  #    ####   ##    #
  #    #        #   #
   ##   ##   ###     ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
clr5x6 :
  #                 #
 ####   ##    ###  ####
  #    ####  ##     #
  #    #       ##   #
   ##   ##   ###     ##
<BLANKLINE>
<BLANKLINE>
clr5x8 :
<BLANKLINE>
  #                 #
 ####   ##    ###  ####
  #    #  #  #      #
  #    ####   ##    #
  #    #        #   #
   ##   ##   ###     ##
<BLANKLINE>
<BLANKLINE>
clr6x10 :
   #                    #
   #                    #
   #                    #
 #####   ###    ####  #####
   #    #   #  #        #
   #    #####   ###     #
   #    #          #    #
    ##   ###   ####      ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
clr6x6 :
  #                    #
 ####    ###    ####  ####
  #     #####  ###     #
  #     #        ###   #
   ##    ###   ####     ##
<BLANKLINE>
<BLANKLINE>
clr6x8 :
   #                    #
   #                    #
 #####   ###    ####  #####
   #    #   #  #        #
   #    #####   ###     #
   #    #          #    #
    ##   ###   ####      ##
<BLANKLINE>
<BLANKLINE>
clr7x8 :
   #                       #
   #                       #
 #####    ####    #####  #####
   #     #    #  #         #
   #     ######   ####     #
   #     #            #    #
    ###   ####   #####      ###
<BLANKLINE>
<BLANKLINE>
clr8x10 :
   #                          #
   #                          #
   #                          #
 ######    #####    #####   ######
   #      #     #  #          #
   #      #######   ####      #
   #      #             #     #
    ###    #####   #####       ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
clr8x8 :
   #                          #
   #                          #
 ######    #####    #####   ######
   #      #     #  #          #
   #      #######   ####      #
   #      #             #     #
    ###    #####   #####       ###
<BLANKLINE>
<BLANKLINE>
coil_cop :
######## #######   #####   ########
## ## ##  ##   #  ##   ##  ## ## ##
   ##     ##      ##          ##
   ##     ####     #####      ##
   ##     ##           ##     ##
   ##     ##   #  ##   ##     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
coinstak :
  O))                     O))
  O))                     O))
O)O) O)   O))     O)))) O)O) O)
  O))   O)   O)) O))      O))
  O))  O))))) O))  O)))   O))
  O))  O)            O))  O))
   O))   O))))   O)) O))   O))
<BLANKLINE>
<BLANKLINE>
cola :
       .                        .
   ...;...                  ...;...
    .'       .-.       .     .'
  .;       .;.-'     .';   .;
.;          `:::'  .' .' .;
                  '
<BLANKLINE>
colossal :
888                      888
888                      888
888                      888
888888  .d88b.  .d8888b  888888
888    d8P  Y8b 88K      888
888    88888888 "Y8888b. 888
Y88b.  Y8b.          X88 Y88b.
 "Y888  "Y8888   88888P'  "Y888
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
com_sen :
######   #######   #####   ######
  ##      ##      ##   ##    ##
  ##      ##      ##         ##
  ##      ####     #####     ##
  ##      ##           ##    ##
  ##      ##      ##   ##    ##
  ##     #######   #####     ##
<BLANKLINE>
<BLANKLINE>
computer :
<BLANKLINE>
eeeee eeee eeeee eeeee
  8   8    8   "   8
  8e  8eee 8eeee   8e
  88  88      88   88
  88  88ee 8ee88   88
<BLANKLINE>
<BLANKLINE>
contessa :
 ,        ,
-+- _  __-+-
 | (/,_)  |
<BLANKLINE>
<BLANKLINE>
contrast :
.%%%%%%..%%%%%%...%%%%...%%%%%%.
...%%....%%......%%........%%...
...%%....%%%%.....%%%%.....%%...
...%%....%%..........%%....%%...
...%%....%%%%%%...%%%%.....%%...
................................
<BLANKLINE>
crawford :
 ______    ___   _____ ______
|      T  /  _] / ___/|      T
|      | /  [_ (   \_ |      |
l_j  l_jY    _] \__  Tl_j  l_j
  |  |  |   [_  /  \ |  |  |
  |  |  |     T \    |  |  |
  l__j  l_____j  \___j  l__j
<BLANKLINE>
<BLANKLINE>
cricket :
 __                  __
|  |_ .-----..-----.|  |_
|   _||  -__||__ --||   _|
|____||_____||_____||____|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
cyberlarge :
 _______ _______ _______ _______
    |    |______ |______    |
    |    |______ ______|    |
<BLANKLINE>
<BLANKLINE>
cybermedium :
___ ____ ____ ___
 |  |___ [__   |
 |  |___ ___]  |
<BLANKLINE>
<BLANKLINE>
cybersmall :
 ___ ____ ____ ___
  |  |=== ====  |
<BLANKLINE>
cygnet :
<BLANKLINE>
 .       .
-|-.-,.--|-
 '-`'--' '-
<BLANKLINE>
<BLANKLINE>
danc4 :
'\   /`  \O/   |_O_|'\   /`
  \ /     Y     _|    \ /
   X     / \  _|  \    X
  /O\  ./   \,    |_  /O\
<BLANKLINE>
dancingfont :
  _____   U _____ u   ____       _____
 |_ " _|  \| ___"|/  / __"| u   |_ " _|
   | |     |  _|"   <\___ \/      | |
  /| |\    | |___    u___) |     /| |\
 u |_|U    |_____|   |____/>>   u |_|U
 _// \\_   <<   >>    )(  (__)  _// \\_
(__) (__) (__) (__)  (__)      (__) (__)
decimal :
116 101 115 116
defleppard :
<BLANKLINE>
<BLANKLINE>
                   ,;          .
                 f#i          ;W
 GEEEEEEEL     .E#t          f#E GEEEEEEEL
 ,;;L#K;;.    i#W,         .E#f  ,;;L#K;;.
    t#E      L#D.         iWW;      t#E
    t#E    :K#Wfff;      L##Lffi    t#E
    t#E    i##WLLLLt    tLLG##L     t#E
    t#E     .E#L          ,W#i      t#E
    t#E       f#E:       j#E.       t#E
    t#E        ,WW;    .D#j         t#E
    t#E         .D#;  ,WK,          t#E
     fE           tt  EG.            fE
      :               ,               :
<BLANKLINE>
<BLANKLINE>
diamond :
  /\\                     /\\
  /\\                     /\\
/\/\ /\   /\\     /\\\\ /\/\ /\
  /\\   /\   /\\ /\\      /\\
  /\\  /\\\\\ /\\  /\\\   /\\
  /\\  /\            /\\  /\\
   /\\   /\\\\   /\\ /\\   /\\
<BLANKLINE>
<BLANKLINE>
dietcola :
<BLANKLINE>
    /                     /
---/---   .-.     .   ---/---
  /     ./.-'_   / \    /
 /      (__.'   / ._)  /
               /
<BLANKLINE>
digital :
+-++-++-++-+
|t||e||s||t|
+-++-++-++-+
<BLANKLINE>
doh :
<BLANKLINE>
<BLANKLINE>
         tttt                                                        tttt
      ttt:::t                                                     ttt:::t
      t:::::t                                                     t:::::t
      t:::::t                                                     t:::::t
ttttttt:::::ttttttt        eeeeeeeeeeee        ssssssssss   ttttttt:::::ttttttt
t:::::::::::::::::t      ee::::::::::::ee    ss::::::::::s  t:::::::::::::::::t
t:::::::::::::::::t     e::::::eeeee:::::eess:::::::::::::s t:::::::::::::::::t
tttttt:::::::tttttt    e::::::e     e:::::es::::::ssss:::::stttttt:::::::tttttt
      t:::::t          e:::::::eeeee::::::e s:::::s  ssssss       t:::::t
      t:::::t          e:::::::::::::::::e    s::::::s            t:::::t
      t:::::t          e::::::eeeeeeeeeee        s::::::s         t:::::t
      t:::::t    tttttte:::::::e           ssssss   s:::::s       t:::::t    tttttt
      t::::::tttt:::::te::::::::e          s:::::ssss::::::s      t::::::tttt:::::t
      tt::::::::::::::t e::::::::eeeeeeee  s::::::::::::::s       tt::::::::::::::t
        tt:::::::::::tt  ee:::::::::::::e   s:::::::::::ss          tt:::::::::::tt
          ttttttttttt      eeeeeeeeeeeeee    sssssssssss              ttttttttttt
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
doom :
 _               _
| |             | |
| |_   ___  ___ | |_
| __| / _ \/ __|| __|
| |_ |  __/\__ \| |_
 \__| \___||___/ \__|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
dotmatrix :
    _                                                    _
   (_)                                                  (_)
 _ (_) _  _        _  _  _  _         _  _  _  _      _ (_) _  _
(_)(_)(_)(_)      (_)(_)(_)(_)_     _(_)(_)(_)(_)    (_)(_)(_)(_)
   (_)           (_) _  _  _ (_)   (_)_  _  _  _        (_)
   (_)     _     (_)(_)(_)(_)(_)     (_)(_)(_)(_)_      (_)     _
   (_)_  _(_)    (_)_  _  _  _        _  _  _  _(_)     (_)_  _(_)
     (_)(_)        (_)(_)(_)(_)      (_)(_)(_)(_)         (_)(_)
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
double :
______ ____ __ ______
| || |||   (( \| || |
  ||  ||==  \\   ||
  ||  ||___\_))  ||
<BLANKLINE>
<BLANKLINE>
doubleshorts :
_____ _____   __ _____
 ||   ||==   ((   ||
 ||   ||___ \_))  ||
<BLANKLINE>
drpepper :
   _               _
 _| |_  ___  ___ _| |_
  | |  / ._><_-<  | |
  |_|  \___./__/  |_|
<BLANKLINE>
<BLANKLINE>
druid :
<BLANKLINE>
######   ######    ####    ######
#             ##  ## # #   #
  ##      ##      ## #       ##
 ###     #######   #####    ###
  ##      ##         # ##    ##
  ##      ##  ##  #  # ##    ##
 ##      ######    #####    ##
<BLANKLINE>
dwhistled :
t st
 X
 X
 X
 X
 X
 .
<BLANKLINE>
test
<BLANKLINE>
<BLANKLINE>
e_fist :
 ######   ######    ####    ######
   ##     ##       ##  ##     ##
   ##     ##       ##         ##
   ##     #####     ####      ##
   ##     ##           ##     ##
   ##     ##       ##  ##     ##
   ##     ######     ###      ##
<BLANKLINE>
<BLANKLINE>
ebbs_1 :
<BLANKLINE>
 ######   ######    #####   ######
   ##     ##       ###        ##
   ##     ####      ###       ##
   ##     ##         ###      ##
   ##     ##          ###     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
ebbs_2 :
######   #######   #####   ######
  ##      ##      ##   ##    ##
  ##      ##      ##         ##
  ##      ####     #####     ##
  ##      ##           ##    ##
  ##      ##      ##   ##    ##
  ##     #######   #####     ##
<BLANKLINE>
<BLANKLINE>
eca :
<BLANKLINE>
 #######  #######   #####   #######
   ###    ####     ### ###    ###
   ###    ######    ###       ###
   ###    ####        ###     ###
   ###    ####     ### ###    ###
   ###    #######   #####     ###
<BLANKLINE>
<BLANKLINE>
eftifont :
<BLANKLINE>
||  _  _ ||
| ]/o\(c'| ]
L| \( \_)L|
<BLANKLINE>
<BLANKLINE>
eftipiti :
<BLANKLINE>
test
<BLANKLINE>
eftirobot :
 _            _
( )          ( )
| |  ___  __ | |
( _)( o_)(_' ( _)
/_\  \(  /__)/_\
<BLANKLINE>
<BLANKLINE>
eftitalic :
<BLANKLINE>
  /7  __  __  /7
 /_7,'o/ (c' /_7
//  |_( /__)//
<BLANKLINE>
<BLANKLINE>
eftiwall :
                   |"|
    ()_()         _|_|_       `  _ ,  '       ()_()
    (o o)         (o o)      -  (o)o)  -      (o o)
ooO--`o'--Ooo-ooO--(_)--Ooo--ooO'(_)--Ooo-ooO--`o'--Ooo-
<BLANKLINE>
eftiwater :
 _        _
 )L __ __ )L
(( (('_))((
<BLANKLINE>
epic :
_________ _______  _______ _________
\__   __/(  ____ \(  ____ \\__   __/
   ) (   | (    \/| (    \/   ) (
   | |   | (__    | (_____    | |
   | |   |  __)   (_____  )   | |
   | |   | (            ) |   | |
   | |   | (____/\/\____) |   | |
   )_(   (_______/\_______)   )_(
<BLANKLINE>
<BLANKLINE>
faces_of :
<BLANKLINE>
 ######   ######    #####   ######
   ## ##  ##  ###  ##  ###    ## ##
   ##     ##    #  ##         ##
   ##     #####    #######    ##
   ##     ##  ##        ##    ##
   ###    ##       ###  ##    ###
    ##    #######   ######     ##
<BLANKLINE>
fair_mea :
###### # ###### # ###### # ###### #
 ### # # ## # # # ## ### #  ### # #
 ### # # ## # # # ## # # #  ### # #
 ### # # #### # # ###### #  ### # #
 ### # # ## # # #  # ### #  ### # #
 ### # # ## # # # ## ### #  ### # #
 ### # # ###### # ###### #  ### # #
 # # # #  # # # #  # # # #  # # # #
<BLANKLINE>
fairligh :
#######  #######   #####   #######
                  ###  ##
  ###    ##       ##         ###
  ###    #####     #####     ###
  ###    ##            ##    ###
  ###    ###  ##  ##  ###    ###
   ###    #####    #####      ###
<BLANKLINE>
<BLANKLINE>
fancy51 :
7357
<BLANKLINE>
fantasy1 :
######    ### #   ### #  ######
  #      ##  #   ##  #     #
 ##     ##       ##       ##
 ##     ####      ####    ##
 ##     ##          ###   ##
 ##  #  ##   #   #   ##   ##  #
 ### #  ### ##   ## ###   ### #
  ###    #### # # ####     ###
<BLANKLINE>
fbr1 :
########  ######    ####   ########
## ## ##   ##      ## ###  ## ## ##
   ##      ###      ##        ##
   ##     ###  #  #   ##      ##
  ####   #######  #######    ####
  ####   #######  #######    ####
  ####   #######  ######     ####
<BLANKLINE>
<BLANKLINE>
fbr12 :
 ######      ###      ###   ######
######      ####     ####  ######
  ##       ##       ##  #    ##
  ##      ##       ##        ##
  ##     ######   #######    ##
  ##     ##            ##    ##
  ###    #######  #######    ###
 ##                         ##
<BLANKLINE>
fbr2 :
########  ######    ###### ########
#  ###    ###  ##  ###  ## #  ###
   ###    ###      ####       ###
   ###    #####     #####     ###
   ###    ###         ####    ###
   ###    ###  ##  ##  ###    ###
   ###    ######    #####     ###
<BLANKLINE>
<BLANKLINE>
fbr_stri :
 ######   ######    #####   ######
 ######   ######   ######   ######
   ##     ##       ##         ##
   ##     ##       ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
fbr_tilt :
 ######      ###      ###   ######
######      ####     ####  ######
  ##       ##       ##  #    ##
  ##      ##       ##        ##
  ##     ######   #######    ##
  ##     ##            ##    ##
  ###    #######  #######    ###
 ##                         ##
<BLANKLINE>
filter :
888888888   ,d8PPPP 88888888  888888888
   '88d     d88ooo  88ooooPp     '88d
  '888    ,88'             d8   '888
'88p      88bdPPP   8888888P  '88p
<BLANKLINE>
<BLANKLINE>
finalass :
 ######  #######   #####    ######
   ##     ##  ##  ##   ##     ##
   ##     ##      ##          ##
   ##     ####     #####      ##
   ##     ##           ##     ##
   ##     ##  ##  ##   ##     ##
   ##    #######   #####      ##
<BLANKLINE>
<BLANKLINE>
fire_font-s :
<BLANKLINE>
    )                  )
 ( /(     (         ( /(
 )\())   ))\   (    )\())
(_))/   /((_)  )\  (_))/
| |_   (_))   ((_) | |_
|  _|  / -_)  (_-< |  _|
 \__|  \___|  /__/  \__|
<BLANKLINE>
<BLANKLINE>
fireing :
######   ######     ####   ######
                   #
#                   #      #
#        ######      #     #
#        #            #    #
#        #            #    #
 #####   ######   ####      #####
<BLANKLINE>
<BLANKLINE>
flipped :
    _  ____  _ __     _
 __| ||    || |  \ __| |
|__  |||_| || || ||__  |
   |_||_||_|\__|_|   |_|
<BLANKLINE>
flyn_sh :
   #     # ##  ##  #    ##    #
# ## #   ######    ## # ## # ## #
# ## # # ##  # ## ### ## # # ## # #
## # # #  ### #   #### # # ## # # #
## ##### ######   #### ### ## #####
#######  ###      ######## #######
######    ###     ######## ######
####     #        ######## ####
<BLANKLINE>
fourtops :
 |       |
~|~/~/(~~|~
 | \/__) |
<BLANKLINE>
<BLANKLINE>
fp1 :
 ######   #######  #######  ######
 #    #                 ##  #    #
   ##      ##       ##        ##
   ##      ####     ######    ##
   ##      ##           ##    ##
   ##      ##       ##  ##    ##
  ####     ######   ######   ####
<BLANKLINE>
<BLANKLINE>
fp2 :
######## #######   #####   ########
## ## ##  ##  ##  ##   ##  ## ## ##
   ##     ##      ##          ##
   ##     ####     #####      ##
   ##     ##           ##     ##
   ##     ##  ##  ##   ##     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
fraktur :
     s                  .x+=:.        s
    :8                 z`    ^%      :8
   .88                    .   <k    .88
  :888ooo      .u       .@8Ned8"   :888ooo
-*8888888   ud8888.   .@^%8888"  -*8888888
  8888    :888'8888. x88:  `)8b.   8888
  8888    d888 '88%" 8888N=*8888   8888
  8888    8888.+"     %8"    R88   8888
 .8888Lu= 8888L        @8Wou 9%   .8888Lu=
 ^%888*   '8888c. .+ .888888P`    ^%888*
   'Y"     "88888%   `   ^"F        'Y"
             "YP'
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
funface :
(o)__(o)           oo_    (o)__(o)
(__  __)   wWw    /  _)-< (__  __)
  (  )     (O)_   \__ `.    (  )
   )(     .' __)     `. |    )(
  (  )   (  _)       _| |   (  )
   )/     `.__)   ,-'   |    )/
  (              (_..--'    (
<BLANKLINE>
funfaces :
(o)__(o)   wWw     oo_    (o)__(o)
(__  __)   (O)_   /  _)-< (__  __)
  (  )     / __)  \__ `.    (  )
   )(     / (        `. |    )(
  (  )   (  _)       _| |   (  )
   )/     \ \_    ,-'   |    )/
  (        \__)  (_..--'    (
<BLANKLINE>
funky_dr :
<BLANKLINE>
<BLANKLINE>
 ######   ######   ######   ######
   ##    ##       ##          ##
   ##    ## ###    #####      ##
   ##    ##            ##     ##
   ##     ######  ######      ##
<BLANKLINE>
<BLANKLINE>
future_1 :
######## #######   #####   ########
## ## ##  ##  ##  ##   ##  ## ## ##
   ##     ##      ##          ##
   ##     ####     #####      ##
   ##     ##           ##     ##
   ##     ##  ##  ##   ##     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
future_2 :
#####     ######   ######  #####
######   #######  #######  ######
    ##   ##       ##           ##
    ##   #######   #####       ##
    ##   ##            ##      ##
    ##   #######  #######      ##
    ##    ######  ######       ##
<BLANKLINE>
<BLANKLINE>
future_3 :
 ######   #######  #######  ######
 #    #                 ##  #    #
   ##      ##       ##        ##
   ##       ###     ######    ##
   ##      ##           ##    ##
   ##      ##       ##  ##    ##
  ####     ######   ######   ####
<BLANKLINE>
<BLANKLINE>
future_4 :
 ######    ###      ##      ######
 ## ##    ##       ## #     ## ##
    ##    ##       ##          ##
     ##   #####     ####        ##
     ##   ##            ##      ##
     ###  ###      ###  ##      ###
     ###    #####   #####       ###
<BLANKLINE>
<BLANKLINE>
future_5 :
 #######   ######    ####   #######
    ##     ##      ###   #     ##
    ##     ##      ##          ##
   ##     ######    ######    ##
   ##     ##            ##    ##
   ##     ##       ##  ##     ##
   ##     #####     ####      ##
<BLANKLINE>
<BLANKLINE>
future_6 :
 ######   ######    ####    ######
######   ####      ### ##  ######
   ##    ###       ##         ##
   ##     # ##      ####      ##
   ##     ##           ##     ##
   ##     ###     ### ###     ##
    #      #####   #####       #
<BLANKLINE>
<BLANKLINE>
future_7 :
 ######   ######   ####     ######
 # ## #   ##      ##  ##    # ## #
   ##     ##      ##          ##
   ##     #####    #####      ##
   ##     ##           ##     ##
   ##     ##      ##   ##     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
future_8 :
<BLANKLINE>
<BLANKLINE>
########  #######  ####### ########
   ##                         ##
   ##     ####     #######    ##
   ##     ##            ##    ##
   ##     #######  #######    ##
<BLANKLINE>
<BLANKLINE>
fuzzy :
 .-.              .-.
.' `.            .' `.
`. .' .--.  .--. `. .'
 : : ' '_.'`._-.' : :
 :_; `.__.'`.__.' :_;
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
gauntlet :
###    #          ###    # ###    #
####   # ####  ## ###      ####   #
####   # #### ### ###      ####   #
####   # ####   # ###  #   ####   #
####   # ####   # ### ###  ####   #
###    # ####   # ###### # ###    #
###    # ####   # #####  # ###    #
###    # ####   # ####   # ###    #
<BLANKLINE>
georgi16 :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
  /                         /
 /M       ____     ____    /M
/MMMMM   6MMMMb   6MMMMb\ /MMMMM
 MM     6M'  `Mb MM'    `  MM
 MM     MM    MM YM.       MM
 MM     MMMMMMMM  YMMMMb   MM
 MM     MM            `Mb  MM
 YM.  , YM    d9 L    ,MM  YM.  ,
  YMMM9  YMMMM9  MYMMMM9    YMMM9
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
georgia11 :
<BLANKLINE>
<BLANKLINE>
  mm                       mm
  MM                       MM
mmMMmm   .gP"Ya  ,pP"Ybd mmMMmm
  MM    ,M'   Yb 8I   `"   MM
  MM    8M"""""" `YMMMa.   MM
  MM    YM.    , L.   I8   MM
  `Mbmo  `Mbmmd' M9mmmP'   `Mbmo
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
ghost :
 .-') _       ('-.     .-')     .-') _
(  OO) )    _(  OO)   ( OO ).  (  OO) )
/     '._  (,------. (_)---\_) /     '._
|'--...__)  |  .---' /    _ |  |'--...__)
'--.  .--'  |  |     \  :` `.  '--.  .--'
   |  |    (|  '--.   '..`''.)    |  |
   |  |     |  .--'  .-._)   \    |  |
   |  |     |  `---. \       /    |  |
   `--'     `------'  `-----'     `--'
<BLANKLINE>
ghost_bo :
#####     # ####   ######  #####
######   #######  #######  ######
    ##   ##       ##           ##
    ##   #######   #####       ##
    ##    #            ##      ##
    ##   #######  #######      ##
    ##    ######  ######       ##
<BLANKLINE>
<BLANKLINE>
ghoulish :
.-,.-.,-.  )\.---.    )\.--.  .-,.-.,-.
) ,, ,. ( (   ,-._(  (   ._.' ) ,, ,. (
\( |(  )/  \  '-,     `-.`.   \( |(  )/
   ) \      ) ,-`    ,_ (  \     ) \
   \ (     (  ``-.  (  '.)  )    \ (
    )/      )..-.(   '._,_.'      )/
<BLANKLINE>
<BLANKLINE>
glenyn :
____ ____ ___  ____
|_ _\| __\| _\ |_ _\
  || |  ]_[__ \  ||
  |/ |___/|___/  |/
<BLANKLINE>
goofy :
_        __        ___       ___        __
(__    __) \    ___)  )  ____) (__    __)
   |  |     |  (__   (  (___      |  |
   |  |     |   __)   \___  \     |  |
   |  |     |  (___   ____)  )    |  |
___|  |____/       )_(      (_____|  |____
<BLANKLINE>
<BLANKLINE>
gothic :
<BLANKLINE>
  ,                ,
 ||               ||
=||=  _-_   _-_, =||=
 ||  || \\ ||_.   ||
 ||  ||/    ~ ||  ||
 \\, \\,/  ,-_-   \\,
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
graceful :
 ____  ____  ____  ____
(_  _)(  __)/ ___)(_  _)
  )(   ) _) \___ \  )(
 (__) (____)(____/ (__)
<BLANKLINE>
gradient :
eeeeeeeee.eeeeee..eeeeee.eeeeeeeee.
@@@@@@@@@:@@@@@@:@@@@@@@:@@@@@@@@@:
---%%%----%%%----%%%--------%%%----
+++&&&++++&&&&&++&&&&&&+++++&&&++++
***|||****|||||***||||||****|||****
===!!!====!!!========!!!====!!!====
###:::####::::::#:::::::####:::####
@@@...@@@@......@......@@@@@...@@@@
<BLANKLINE>
<BLANKLINE>
graffiti :
  __                     __
_/  |_   ____    _______/  |_
\   __\_/ __ \  /  ___/\   __\
 |  |  \  ___/  \___ \  |  |
 |__|   \___  >/____  > |__|
            \/      \/
<BLANKLINE>
grand_pr :
######   ######     ##     ######
  ##     ##       ##  ##     ##
  ##     ##       ##         ##
  ##     ####       ##       ##
  ##     ##           ##     ##
  ##     ##       ##  ##     ##
  ##     ######     ##       ##
<BLANKLINE>
<BLANKLINE>
greek :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 ___  ___   ____  ___
(   )/ __) /  ._)(   )
 | | > _) ( () )  | |
  \_)\___) \__/    \_)
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
green_be :
 ######  ### ###    # ###   ######
 # ## #   ##  ##   ##  ##   # ## #
   ##     #        ##         ##
   ##     ## ##     ####      ##
   ##     ##           ##     ##
   ##     ##  ##   ##  ##     ##
   ##     #  ###   ## ##      ##
<BLANKLINE>
<BLANKLINE>
h4k3r :
7357
<BLANKLINE>
hades :
#######  #######   ######  #######
#######  ##  ###  #####    #######
  ###    ##   ##  ##         ###
  ###    ###       #####     ###
  ###    ####         ###    ###
  ####   ##   ##  ###  ##    ####
  ####   #######  ######     ####
<BLANKLINE>
<BLANKLINE>
heartleft :
 .-.-.  .-.-.  .-.-.  .-.-.
( t .' ( e .' ( s .' ( t .'
 `.(    `.(    `.(    `.(
<BLANKLINE>
<BLANKLINE>
heartright :
.-.-.  .-.-.  .-.-.  .-.-.
'. t ) '. e ) '. s ) '. t )
  ).'    ).'    ).'    ).'
<BLANKLINE>
<BLANKLINE>
heavy_me :
 ######  #######   #####    ######
 # ## #   ##  ##  ##   ##   # ## #
   ##     ##      ###         ##
   ##     ####      ###       ##
   ##     ##          ###     ##
   ##     ##  ##  ##   ##     ##
  ####   #######   #####     ####
<BLANKLINE>
<BLANKLINE>
henry3d :
  _                          _
 FJ_      ____      ____    FJ_
J  _|    F __ J    F ___J  J  _|
| |-'   | _____J  | '----_ | |-'
F |__-. F L___--. )-____  LF |__-.
\_____/J\______/FJ\______/F\_____/
J_____F J______F  J______F J_____F
<BLANKLINE>
<BLANKLINE>
heroboti :
<BLANKLINE>
 ##                 ###     ##
 ####      #####   ##       ####
 ####     #######  ######   ####
 ##       ##  ###   ######  ##
 ##   ##  ##  ##        ##  ##   ##
 #######  ####     #######  #######
  #####    #####   ######    #####
<BLANKLINE>
high_noo :
# # # #  # ## ##  # # # #  # # # #
# # # #    #      # # # #  # # # #
# # # #  #    # # # # # #  # # # #
# # # #  ###  ##  # # # #  # # # #
# # # #  #   ## # # # # #  # # # #
# # # #  # ##     # # # #  # # # #
# # # #  # #####  # # # #  # # # #
#######       #   # # # ## #######
<BLANKLINE>
hills :
### ####  #       # # # ## ### ####
# #####  # ##     # ###### # #####
# ###### ## ###   # ###### # ######
### ###  ##### ## # # # ## ### ###
# ### #  ### ###  # # # #  # ### #
### #### # ### ## # # # #  ### ####
# ### ## # # ###  # # # #  # ### ##
# # ###  ### # #  # # # #  # # ###
<BLANKLINE>
hollywood :
<BLANKLINE>
         /'                               /'
     --/'--                           --/'--
     /'         ____      ____        /'
   /'         /'    )   /'    )--   /'
 /'         /(___,/'   '---,      /'
(__        (________ (___,/      (__
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
home_pak :
<BLANKLINE>
 #######  #######   #####   #######
   ###    # ##     ### ###    ###
   ###    ######    ###       ###
   ###    ####        ###     ###
   ###    ####     ### ###    ###
   ###    #######   #####     ###
<BLANKLINE>
<BLANKLINE>
horizontalleft :
 _         _______        __   _
| /_____  | ._ _. |  .-. \  \ | /_____
| ______/ | \ v / | / _ \_\ | | ______/
|_\       |_/   \_| |_\`.___/ |_\
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
horizontalright :
       _   _______   __              _
 _____\ | | ._ _. | /  / .-.   _____\ |
\______ | | \ v / | | /_/ _ \ \______ |
      /_| |_/   \_| \___.'/_|       /_|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
house_of :
<BLANKLINE>
 #######  ######     #####  #######
   ##     ##        ##        ##
   ##     ##         ###      ##
   ##      ###         ##     ##
   ##     ##       ##  ##     ##
  ###     ######   #####     ###
<BLANKLINE>
<BLANKLINE>
hypa_bal :
######   ######   ######   ######
  ##     ##       ##         ##
  ##     ##       ##         ##
  ##     ####     ######     ##
  ##     ##           ##     ##
  ##     ##           ##     ##
  ##     ######   ######     ##
<BLANKLINE>
<BLANKLINE>
hyper :
 ######   ######   ####     ######
 # ## #   ##      ##  ##    # ## #
   ##     ##      ##          ##
   ##     #####    #####      ##
   ##     ##           ##     ##
   ##     ##      ##   ##     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
icl-1900 :
test
 *
<BLANKLINE>
* **
<BLANKLINE>
  *
*  *
<BLANKLINE>
 *
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
impossible :
       _                _              _               _
      /\ \             /\ \           / /\            /\ \
      \_\ \           /  \ \         / /  \           \_\ \
      /\__ \         / /\ \ \       / / /\ \__        /\__ \
     / /_ \ \       / / /\ \_\     / / /\ \___\      / /_ \ \
    / / /\ \ \     / /_/_ \/_/     \ \ \ \/___/     / / /\ \ \
   / / /  \/_/    / /____/\         \ \ \          / / /  \/_/
  / / /          / /\____\/     _    \ \ \        / / /
 / / /          / / /______    /_/\__/ / /       / / /
/_/ /          / / /_______\   \ \/___/ /       /_/ /
\_\/           \/__________/    \_____\/        \_\/
<BLANKLINE>
<BLANKLINE>
inc_raw :
##     # ##     # ##     # ##     #
###  ### #  ##### #  ##  # ###  ###
###  ### #  ##### #  ##### ###  ###
###  ### #    ### ##    ## ###  ###
###  ### #  ##### #####  # ###  ###
###  ### #  ##### #####  # ###  ###
###  ### #  ##### #  ##  # ###  ###
### #### #     ## #     ## ### ####
<BLANKLINE>
invita :
<BLANKLINE>
<BLANKLINE>
_/_   _  _  _/_
(__ _(/_/_)_(__
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
isometric1 :
      ___           ___           ___           ___
     /\  \         /\  \         /\  \         /\  \
     \:\  \       /::\  \       /::\  \        \:\  \
      \:\  \     /:/\:\  \     /:/\ \  \        \:\  \
      /::\  \   /::\~\:\  \   _\:\~\ \  \       /::\  \
     /:/\:\__\ /:/\:\ \:\__\ /\ \:\ \ \__\     /:/\:\__\
    /:/  \/__/ \:\~\:\ \/__/ \:\ \:\ \/__/    /:/  \/__/
   /:/  /       \:\ \:\__\    \:\ \:\__\     /:/  /
   \/__/         \:\ \/__/     \:\/:/  /     \/__/
                  \:\__\        \::/  /
                   \/__/         \/__/
<BLANKLINE>
isometric2 :
                    ___           ___
                   /\__\         /\__\
      ___         /:/ _/_       /:/ _/_         ___
     /\__\       /:/ /\__\     /:/ /\  \       /\__\
    /:/  /      /:/ /:/ _/_   /:/ /::\  \     /:/  /
   /:/__/      /:/_/:/ /\__\ /:/_/:/\:\__\   /:/__/
  /::\  \      \:\/:/ /:/  / \:\/:/ /:/  /  /::\  \
 /:/\:\  \      \::/_/:/  /   \::/ /:/  /  /:/\:\  \
 \/__\:\  \      \:\/:/  /     \/_/:/  /   \/__\:\  \
      \:\__\      \::/  /        /:/  /         \:\__\
       \/__/       \/__/         \/__/           \/__/
<BLANKLINE>
isometric3 :
                  ___           ___
      ___        /  /\         /  /\          ___
     /  /\      /  /:/_       /  /:/_        /  /\
    /  /:/     /  /:/ /\     /  /:/ /\      /  /:/
   /  /:/     /  /:/ /:/_   /  /:/ /::\    /  /:/
  /  /::\    /__/:/ /:/ /\ /__/:/ /:/\:\  /  /::\
 /__/:/\:\   \  \:\/:/ /:/ \  \:\/:/~/:/ /__/:/\:\
 \__\/  \:\   \  \::/ /:/   \  \::/ /:/  \__\/  \:\
      \  \:\   \  \:\/:/     \__\/ /:/        \  \:\
       \__\/    \  \::/        /__/:/          \__\/
                 \__\/         \__\/
<BLANKLINE>
isometric4 :
                    ___           ___
      ___          /  /\         /  /\          ___
     /__/\        /  /::\       /  /::\        /__/\
     \  \:\      /  /:/\:\     /__/:/\:\       \  \:\
      \__\:\    /  /::\ \:\   _\_ \:\ \:\       \__\:\
      /  /::\  /__/:/\:\ \:\ /__/\ \:\ \:\      /  /::\
     /  /:/\:\ \  \:\ \:\_\/ \  \:\ \:\_\/     /  /:/\:\
    /  /:/__\/  \  \:\ \:\    \  \:\_\:\      /  /:/__\/
   /__/:/        \  \:\_\/     \  \:\/:/     /__/:/
   \__\/          \  \:\        \  \::/      \__\/
                   \__\/         \__\/
<BLANKLINE>
italic :
<BLANKLINE>
_/  _   _ _/
/  (- _)  /
<BLANKLINE>
<BLANKLINE>
italics :
 ######   # ####     ###    ######
   #        #       #   #     #
   #       #      # #         #
   #      ####       ##       #
  #        # #         #     #
  #       #        #   #     #
  #       #####     ###      #
<BLANKLINE>
<BLANKLINE>
jacky :
 ________    _____    _____   ________
(___  ___)  / ___/   / ____\ (___  ___)
    ) )    ( (__    ( (___       ) )
   ( (      ) __)    \___ \     ( (
    ) )    ( (           ) )     ) )
   ( (      \ \___   ___/ /     ( (
   /__\      \____\ /____/      /__\
<BLANKLINE>
<BLANKLINE>
jazmine :
<BLANKLINE>
  o                  o
  8                  8
 o8P .oPYo. .oPYo.  o8P
  8  8oooo8 Yb..     8
  8  8.       'Yb.   8
  8  `Yooo' `YooP'   8
::..::.....::.....:::..:
::::::::::::::::::::::::
::::::::::::::::::::::::
<BLANKLINE>
katakana :
     #                ##########      #
  #######  ########## #        #   #######
   # #             #          #     # #
   # #            #          #      # #
##########     # #          #    ##########
     #          #         ##          #
     #           #      ##            #
<BLANKLINE>
<BLANKLINE>
keyboard :
<BLANKLINE>
 _______  _______  _______  _______
|\     /||\     /||\     /||\     /|
| +---+ || +---+ || +---+ || +---+ |
| |   | || |   | || |   | || |   | |
| |t  | || |e  | || |s  | || |t  | |
| +---+ || +---+ || +---+ || +---+ |
|/_____\||/_____\||/_____\||/_____\|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
kgames_i :
<BLANKLINE>
 ######   ######    ####    ######
   ##     ##       ##         ##
   ##     #####     ####      ##
   ##     ##           ##     ##
   ##     ##           ##     ##
   ##     ######    ####      ##
<BLANKLINE>
<BLANKLINE>
kik_star :
 ######   ######   ####     ######
   ##     ##      ##  ##      ##
   ##     ##      ##          ##
   ##     #####    #####      ##
   ##     ##           ##     ##
   ##     ##      ##   ##     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
knob :
         _  _________  _   _____          _
 _______| |(  _   _  )( ) (  _  ) _______| |
(_______  || | | | | || |_| | | |(_______  |
        |_|(_) (_) (_)(_____) (_)        |_|
<BLANKLINE>
krak_out :
<BLANKLINE>
 ######  #####      #####   ######
   ##    ##        ##   #     ##
   ##    ######   ##          ##
   ##    ##       #######     ##
   ##    ##             #     ##
   ##    #######  #######     ##
<BLANKLINE>
<BLANKLINE>
larry3d :
 __                       __
/\ \__                   /\ \__
\ \ ,_\     __     ____  \ \ ,_\
 \ \ \/   /'__`\  /',__\  \ \ \/
  \ \ \_ /\  __/ /\__, `\  \ \ \_
   \ \__\\ \____\\/\____/   \ \__\
    \/__/ \/____/ \/___/     \/__/
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
lcd :
<BLANKLINE>
  |                 |
 -+-   -       -   -+-
  |   |/       \    |
   -   --      -     -
<BLANKLINE>
<BLANKLINE>
lean :
<BLANKLINE>
   _/                                   _/
_/_/_/_/       _/_/         _/_/_/   _/_/_/_/
 _/         _/_/_/_/     _/_/         _/
_/         _/               _/_/     _/
 _/_/       _/_/_/     _/_/_/         _/_/
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
letters :
tt                 tt
tt      eee   sss  tt
tttt  ee   e s     tttt
tt    eeeee   sss  tt
 tttt  eeeee     s  tttt
              sss
<BLANKLINE>
lildevil :
(`-')       (`-')  _  (`-').-> (`-')
( OO).->    ( OO).-/  ( OO)_   ( OO).->
/    '._   (,------. (_)--\_)  /    '._
|'--...__)  |  .---' /    _ /  |'--...__)
`--.  .--' (|  '--.  \_..`--.  `--.  .--'
   |  |     |  .--'  .-._)   \    |  |
   |  |     |  `---. \       /    |  |
   `--'     `------'  `-----'     `--'
<BLANKLINE>
lineblocks :
_______  ______  ______  _______
  | |   | |     / |        | |
  | |   | |---- '------.   | |
  |_|   |_|____  ____|_/   |_|
<BLANKLINE>
<BLANKLINE>
lockergnome :
 :|             :|
:::| :~~/ <::< :::|
 :|  :::, >::>  :|
<BLANKLINE>
madrid :
|-         |-
|  /=\ /== |
\= \=  ==/ \=
<BLANKLINE>
<BLANKLINE>
marquee :
  .::                     .::
  .::                     .::
.:.: .:   .::     .:::: .:.: .:
  .::   .:   .:: .::      .::
  .::  .::::: .::  .:::   .::
  .::  .:            .::  .::
   .::   .::::   .:: .::   .::
<BLANKLINE>
<BLANKLINE>
maxfour :
 |       |
~|~/~/(~~|~
 | \/__) |
<BLANKLINE>
<BLANKLINE>
merlin1 :
 ___________    _______    ________   ___________
("     _   ")  /"     "|  /"       ) ("     _   ")
 )__/  \\__/  (: ______) (:   \___/   )__/  \\__/
    \\_ /      \/    |    \___  \        \\_ /
    |.  |      // ___)_    __/  \\       |.  |
    \:  |     (:      "|  /" \   :)      \:  |
     \__|      \_______) (_______/        \__|
<BLANKLINE>
<BLANKLINE>
merlin2 :
    _         _          _         _
 __/\\__   __/\\___     /\\__   __/\\__
(__  __)) (_  ____))   /    \\ (__  __))
  /  \\    /  ._))    _\  \_//   /  \\
 /:.  \\  /:. ||___  // \:.\    /:.  \\
 \__  //  \  _____)) \\__  /    \__  //
    \//    \//          \\/        \//
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
mike :
  _   _     _
   | |/ //   |
<BLANKLINE>
<BLANKLINE>
mini :
<BLANKLINE>
_|_  _   _ _|_
 |_ (/_ _>  |_
<BLANKLINE>
<BLANKLINE>
modular :
 _______  _______  _______  _______
|       ||       ||       ||       |
|_     _||    ___||  _____||_     _|
  |   |  |   |___ | |_____   |   |
  |   |  |    ___||_____  |  |   |
  |   |  |   |___  _____| |  |   |
  |___|  |_______||_______|  |___|
<BLANKLINE>
morse :
- . ... -
<BLANKLINE>
moscow :
<BLANKLINE>
##### #####  #### #####
  #   #     #       #
  #   ####  #       #
  #   #     #       #
  #   #####  ####   #
<BLANKLINE>
muzzle :
       __
>>|<< |   |<< >>|<<
  |   |<< --    |
  |   |__ >>|   |
<BLANKLINE>
nancyj :
  dP                       dP
  88                       88
d8888P .d8888b. .d8888b. d8888P
  88   88ooood8 Y8ooooo.   88
  88   88.  ...       88   88
  dP   `88888P' `88888P'   dP
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
nancyj-fancy :
  dP                       dP
  88                       88
d8888P .d8888b. .d8888b. d8888P
  88   88ooood8 Y8ooooo.   88
  88   88.  ...       88   88
  dP   `88888P' `88888P'   dP
<BLANKLINE>
<BLANKLINE>
nancyj-underlined :
  dP                       dP
  88                       88
d8888P .d8888b. .d8888b. d8888P
  88   88ooood8 Y8ooooo.   88
  88   88.  ...       88   88
  dP   `88888P' `88888P'   dP
oooooooooooooooooooooooooooooooo
<BLANKLINE>
<BLANKLINE>
nfi1 :
           ####
  ####     ####   ##         ####
    ####   ####   ####         ####
########          ####     ########
    ####          ####         ####
  ####            ##         ####
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
nipples :
  {__                     {__
  {__                     {__
{_{_ {_   {__     {____ {_{_ {_
  {__   {_   {__ {__      {__
  {__  {_____ {__  {___   {__
  {__  {_            {__  {__
   {__   {____   {__ {__   {__
<BLANKLINE>
<BLANKLINE>
nscript :
<BLANKLINE>
   I8                         I8
   I8                         I8
88888888                   88888888
   I8                         I8
   I8     ,ggg,     ,g,       I8
   I8    i8" "8i   ,8'8,      I8
  ,I8,   I8, ,8I  ,8'  Yb    ,I8,
 ,d88b,  `YbadP' ,8'_   8)  ,d88b,
88P""Y88888P"Y888P' "YY8P8P88P""Y88
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
nvscript :
<BLANKLINE>
   I8                         I8
   I8                         I8
88888888                   88888888
   I8                         I8
   I8     ,ggg,     ,g,       I8
   I8    i8" "8i   ,8'8,      I8
  ,I8,   I8, ,8I  ,8'  Yb    ,I8,
 ,d88b,  `YbadP' ,8'_   8)  ,d88b,
 8P""Y8 888P"Y888P' "YY8P8P 8P""Y8
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
o8 :
  o8                             o8
o888oo  ooooooooo8  oooooooo8  o888oo
 888   888oooooo8  888ooooooo   888
 888   888                 888  888
  888o   88oooo888 88oooooo88    888o
<BLANKLINE>
<BLANKLINE>
octal :
164 145 163 164
ogre :
 _               _
| |_   ___  ___ | |_
| __| / _ \/ __|| __|
| |_ |  __/\__ \| |_
 \__| \___||___/ \__|
<BLANKLINE>
<BLANKLINE>
oldbanner :
<BLANKLINE>
##### ######  ####  #####
  #   #      #        #
  #   #####   ####    #
  #   #           #   #
  #   #      #    #   #
  #   ######  ####    #
<BLANKLINE>
os2 :
_oo____________________oo____
_oo_____ooooo___oooo___oo____
oooo___oo____o_oo___o_oooo___
_oo____ooooooo___oo____oo____
_oo__o_oo______o___oo__oo__o_
__ooo___ooooo___oooo____ooo__
_____________________________
<BLANKLINE>
pawp :
<BLANKLINE>
 _                 _
(_)_   ____  ____ (_)_
(___) (____)(____)(___)
(_)  (_)_(_)(_)__ (_)
(_)_ (__)__  _(__)(_)_
 (__) (____)(____) (__)
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
peaks :
  /^^                     /^^
  /^^                     /^^
/^/^ /^   /^^     /^^^^ /^/^ /^
  /^^   /^   /^^ /^^      /^^
  /^^  /^^^^^ /^^  /^^^   /^^
  /^^  /^            /^^  /^^
   /^^   /^^^^   /^^ /^^   /^^
<BLANKLINE>
<BLANKLINE>
pebbles :
<BLANKLINE>
<BLANKLINE>
  O                 O
 oOo               oOo
  o   .oOo. .oOo    o
  O   OooO' `Ooo.   O
  o   O         O   o
  `oO `OoO' `OoO'   `oO
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
pepper :
<BLANKLINE>
_/_ _   __/_
/  /_'_\ /
<BLANKLINE>
<BLANKLINE>
poison :
<BLANKLINE>
@@@@@@@  @@@@@@@@   @@@@@@   @@@@@@@
@@@@@@@  @@@@@@@@  @@@@@@@   @@@@@@@
  @@!    @@!       !@@         @@!
  !@!    !@!       !@!         !@!
  @!!    @!!!:!    !!@@!!      @!!
  !!!    !!!!!:     !!@!!!     !!!
  !!:    !!:            !:!    !!:
  :!:    :!:           !:!     :!:
   ::     :: ::::  :::: ::      ::
   :     : :: ::   :: : :       :
<BLANKLINE>
<BLANKLINE>
puffy :
 _                 _
( )_              ( )_
| ,_)   __    ___ | ,_)
| |   /'__`\/',__)| |
| |_ (  ___/\__, \| |_
`\__)`\____)(____/`\__)
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
puzzle :
     _         _         _         _
   _( )__    _( )__    _( )__    _( )__
 _|     _| _|     _| _|     _| _|     _|
(_ T _ (_ (_ E _ (_ (_ S _ (_ (_ T _ (_
  |_( )__|  |_( )__|  |_( )__|  |_( )__|
<BLANKLINE>
pyramid :
  ^    ^    ^    ^
 /t\  /e\  /s\  /t\
<___><___><___><___>
<BLANKLINE>
rammstein :
<BLANKLINE>
   __     ______   ______     __
 _|  |_  |   ___| |   ___|  _|  |_
|_    _| |   ___|  `-.`-.  |_    _|
  |__|   |______| |______|   |__|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
rectangles :
<BLANKLINE>
 _              _
| |_  ___  ___ | |_
|  _|| -_||_ -||  _|
|_|  |___||___||_|
<BLANKLINE>
<BLANKLINE>
red_phoenix :
  __                        __
_/  |_    ____     ______ _/  |_
\   __\ _/ __ \   /  ___/ \   __\
 |  |   \  ___/   \___ \   |  |
 |__|    \___  > /____  >  |__|
             \/       \/
<BLANKLINE>
<BLANKLINE>
rev :
==========================
==========================
==========================
==  ==================  ==
=    ===   ====   ===    =
==  ===  =  ==  =  ===  ==
==  ===     ===  =====  ==
==  ===  =======  ====  ==
==  ===  =  ==  =  ===  ==
==   ===   ====   ====   =
==========================
<BLANKLINE>
roman :
    .                          .
  .o8                        .o8
.o888oo  .ooooo.   .oooo.o .o888oo
  888   d88' `88b d88(  "8   888
  888   888ooo888 `"Y88b.    888
  888 . 888    .o o.  )88b   888 .
  "888" `Y8bod8P' 8""888P'   "888"
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
rot13 :
grfg
rounded :
<BLANKLINE>
   _                   _
 _| |_  _____   ___  _| |_
(_   _)| ___ | /___)(_   _)
  | |_ | ____||___ |  | |_
   \__)|_____)(___/    \__)
<BLANKLINE>
<BLANKLINE>
rowancap :
 dMMMMMMP     dMMMMMP    .dMMMb  dMMMMMMP
   dMP       dMP        dMP" VP    dMP
  dMP       dMMMP       VMMMb     dMP
 dMP       dMP        dP .dMP    dMP
dMP       dMMMMMP     VMMMP"    dMP
<BLANKLINE>
<BLANKLINE>
rozzo :
  d8                   d8
 d88    ,e e,   dP"Y  d88
d88888 d88 88b C88b  d88888
 888   888   ,  Y88D  888
 888    "YeeP" d,dP   888
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
runyc :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
/|\ |\/| /  /|\
 |  |  | --  |
 |  |  |  /  |
<BLANKLINE>
santaclara :
<BLANKLINE>
 _/_        _/_
 /   _  (   /
(__ (/_/_)_(__
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
sblood :
 @@@@@@@ @@@@@@@@  @@@@@@ @@@@@@@
   @@!   @@!      !@@       @@!
   @!!   @!!!:!    !@@!!    @!!
   !!:   !!:          !:!   !!:
    :    : :: ::: ::.: :     :
<BLANKLINE>
<BLANKLINE>
script :
<BLANKLINE>
<BLANKLINE>
_|_  _   ,  _|_
 |  |/  / \_ |
 |_/|__/ \/  |_/
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
serifcap :
 ____  ___  ___  ____
(_  _)(  _)/ __)(_  _)
  )(   ) _)\__ \  )(
 (__) (___)(___/ (__)
<BLANKLINE>
shadow :
 |                |
 __|   _ \   __|  __|
 |     __/ \__ \  |
\__| \___| ____/ \__|
<BLANKLINE>
<BLANKLINE>
shimrod :
.           .
|           |
|-  ,-. ,-. |-
|   |-' `-. |
`-' `-' `-' `-'
<BLANKLINE>
<BLANKLINE>
short :
|- _  _|-
|_(/__\|_
<BLANKLINE>
<BLANKLINE>
slant :
   __                  __
  / /_  ___    _____  / /_
 / __/ / _ \  / ___/ / __/
/ /_  /  __/ (__  ) / /_
\__/  \___/ /____/  \__/
<BLANKLINE>
<BLANKLINE>
slide :
 #|                #|
##HH|  #H|   #HH| ##HH|
 #|   ##HH| ##H|   #|
 #|   ##       H|  #|
 #H|   #HH| ##H|   #H|
<BLANKLINE>
<BLANKLINE>
slscript :
<BLANKLINE>
 _/_        _/_
 /   _  _   /
<__ </_/_)_<__
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
small :
 _             _
| |_  ___  ___| |_
|  _|/ -_)(_-<|  _|
 \__|\___|/__/ \__|
<BLANKLINE>
smallcaps :
 _____  ___    ___   _____
)__ __() __(  (  _( )__ __(
  | |  | _)   _) \    | |
  )_(  )___( )____)   )_(
<BLANKLINE>
<BLANKLINE>
smisome1 :
    ___       ___       ___       ___
   /\  \     /\  \     /\  \     /\  \
   \:\  \   /::\  \   /::\  \    \:\  \
   /::\__\ /::\:\__\ /\:\:\__\   /::\__\
  /:/\/__/ \:\:\/  / \:\:\/__/  /:/\/__/
  \/__/     \:\/  /   \::/  /   \/__/
             \/__/     \/__/
<BLANKLINE>
smkeyboard :
 ____  ____  ____  ____
||t ||||e ||||s ||||t ||
||__||||__||||__||||__||
|/__\||/__\||/__\||/__\|
<BLANKLINE>
smpoison :
<BLANKLINE>
@@@@@@@ @@@@@@@@  @@@@@@ @@@@@@@
  @!!   @@!      !@@       @!!
  @!!   @!!!:!    !@@!!    @!!
  !!:   !!:          !:!   !!:
   :    : :: ::  ::.: :     :
<BLANKLINE>
<BLANKLINE>
smscript :
<BLANKLINE>
_|_  _  ,  _|_
 |  |/ / \_ |
 |_/|_/ \/  |_/
<BLANKLINE>
<BLANKLINE>
smshadow :
 |               |
  _|   -_) (_-<   _|
\__| \___| ___/ \__|
<BLANKLINE>
<BLANKLINE>
smslant :
  __             __
 / /_ ___   ___ / /_
/ __// -_) (_-</ __/
\__/ \__/ /___/\__/
<BLANKLINE>
<BLANKLINE>
smtengwar :
    ,'
|~)  | (~) |~)
|       /  |
<BLANKLINE>
soft :
<BLANKLINE>
  ,--.                     ,--.
,-'  '-.  ,---.   ,---.  ,-'  '-.
'-.  .-' | .-. : (  .-'  '-.  .-'
  |  |   \   --. .-'  `)   |  |
  `--'    `----' `----'    `--'
<BLANKLINE>
<BLANKLINE>
speed :
_____               _____
__  /______ __________  /_
_  __/_  _ \__  ___/_  __/
/ /_  /  __/_(__  ) / /_
\__/  \___/ /____/  \__/
<BLANKLINE>
<BLANKLINE>
spliff :
 ____  _____  _____  ____
/    \/   __\/  ___>/    \
\-  -/|   __||___  |\-  -/
 |__| \_____/<_____/ |__|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
stacey :
______________________________
7      77     77     77      7
!__  __!|  ___!|  ___!!__  __!
  7  7  |  __|_!__   7  7  7
  |  |  |     77     |  |  |
  !__!  !_____!!_____!  !__!
<BLANKLINE>
<BLANKLINE>
stampate :
.          .
|- ,-. ,-. |-
|  |-' `-. |
`' `-' `-' `'
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
stampatello :
.          .
|- ,-. ,-. |-
|  |-' `-. |
`' `-' `-' `'
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
standard :
 _               _
| |_   ___  ___ | |_
| __| / _ \/ __|| __|
| |_ |  __/\__ \| |_
 \__| \___||___/ \__|
<BLANKLINE>
<BLANKLINE>
starwars :
.___________. _______      _______..___________.
|           ||   ____|    /       ||           |
`---|  |----`|  |__      |   (----``---|  |----`
    |  |     |   __|      \   \        |  |
    |  |     |  |____ .----)   |       |  |
    |__|     |_______||_______/        |__|
<BLANKLINE>
<BLANKLINE>
stellar :
  `..                     `..
  `..                     `..
`.`. `.   `..     `.... `.`. `.
  `..   `.   `.. `..      `..
  `..  `..... `..  `...   `..
  `..  `.            `..  `..
   `..   `....   `.. `..   `..
<BLANKLINE>
<BLANKLINE>
stforek :
 _____   ___    __   _____
|_   _| | __| /' _/ |_   _|
  | |   | _|  `._`.   | |
  |_|   |___| |___/   |_|
<BLANKLINE>
stop :
<BLANKLINE>
 _                  _
| |_    ____   ___ | |_
|  _)  / _  ) /___)|  _)
| |__ ( (/ / |___ || |__
 \___) \____)(___/  \___)
<BLANKLINE>
<BLANKLINE>
straight :
<BLANKLINE>
|_  _  _ |_
|_ (- _) |_
<BLANKLINE>
<BLANKLINE>
sub-zero :
 ______   ______     ______     ______
/\__  _\ /\  ___\   /\  ___\   /\__  _\
\/_/\ \/ \ \  __\   \ \___  \  \/_/\ \/
   \ \_\  \ \_____\  \/\_____\    \ \_\
    \/_/   \/_____/   \/_____/     \/_/
<BLANKLINE>
<BLANKLINE>
swampland :
 _________   ______       ______       _________
/________/\ /_____/\     /_____/\     /________/\
\__.::.__\/ \::::_\/_    \::::_\/_    \__.::.__\/
   \::\ \    \:\/___/\    \:\/___/\      \::\ \
    \::\ \    \::___\/_    \_::._\:\      \::\ \
     \::\ \    \:\____/\     /____\:\      \::\ \
      \__\/     \_____\/     \_____\/       \__\/
<BLANKLINE>
<BLANKLINE>
swan :
<BLANKLINE>
<BLANKLINE>
 .            .
_|_          _|_
 |   .-. .--. |
 |  (.-' `--. |
 `-' `--'`--' `-'
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
sweet :
 ___                            ___
(   )                          (   )
 | |_       .--.       .--.     | |_
(   __)    /    \    /  _  \   (   __)
 | |      |  .-. ;  . .' `. ;   | |
 | | ___  |  | | |  | '   | |   | | ___
 | |(   ) |  |/  |  _\_`.(___)  | |(   )
 | | | |  |  ' _.' (   ). '.    | | | |
 | ' | |  |  .'.-.  | |  `\ |   | ' | |
 ' `-' ;  '  `-' /  ; '._,' '   ' `-' ;
  `.__.    `.__.'    '.___.'     `.__.
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
tanja :
  t)                     t)
t)tTTT                 t)tTTT
  t)   e)EEEEE  s)SSSS   t)
  t)   e)EEEE  s)SSSS    t)
  t)   e)           s)   t)
  t)T   e)EEEE s)SSSS    t)T
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
tengwar :
             .dP'
           dP'
<BLANKLINE>
`Yb.d888b   'Yb   .d888b.  `Yb.d888b
 88'    8Y   88   8'   `Yb  88'    8Y
 88     8P   88   Yb.   88  88     8P
 88   ,dP   .8P       .dP   88   ,dP
 88                 .dP'    88
 88               .dP'      88
.8P                        .8P
<BLANKLINE>
term :
test
thick :
 w               w
w8ww .d88b d88b w8ww
 8   8.dP' `Yb.  8
 Y8P `Y88P Y88P  Y8P
<BLANKLINE>
<BLANKLINE>
thin :
<BLANKLINE>
|              |
|--- ,---.,---.|---
|    |---'`---.|
`---'`---'`---'`---'
<BLANKLINE>
<BLANKLINE>
threepoint :
_|_ _  __|_
 | (/__\ |
<BLANKLINE>
<BLANKLINE>
ticks :
___/\/\________________________________/\/\_____
_/\/\/\/\/\____/\/\/\______/\/\/\/\__/\/\/\/\/\_
___/\/\______/\/\/\/\/\__/\/\/\/\______/\/\_____
___/\/\______/\/\______________/\/\____/\/\_____
___/\/\/\______/\/\/\/\__/\/\/\/\______/\/\/\___
________________________________________________
<BLANKLINE>
ticksslant :
     ___/\/\_____     ____________     ____________     ___/\/\_____
    _/\/\/\/\/\_     ___/\/\/\___     ___/\/\/\/\_     _/\/\/\/\/\_
   ___/\/\_____     _/\/\/\/\/\_     _/\/\/\/\___     ___/\/\_____
  ___/\/\_____     _/\/\_______     _______/\/\_     ___/\/\_____
 ___/\/\/\___     ___/\/\/\/\_     _/\/\/\/\___     ___/\/\/\___
____________     ____________     ____________     ____________
<BLANKLINE>
tiles :
  [..                     [..
  [..                     [..
[.[. [.   [..     [.... [.[. [.
  [..   [.   [.. [..      [..
  [..  [..... [..  [...   [..
  [..  [.            [..  [..
   [..   [....   [.. [..   [..
<BLANKLINE>
<BLANKLINE>
tinker-toy :
 o           o
 |           |
-o- o-o o-o -o-
 |  |-'  \   |
 o  o-o o-o  o
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
tombstone :
 ___ __,  _, ___
  |  |_  (_   |
  |  |   , )  |
  ~  ~~~  ~   ~
<BLANKLINE>
<BLANKLINE>
trek :
  dBBBBBBP     dBBBP   .dBBBBP  dBBBBBBP
                       BP
   dBP       dBBP      `BBBBb    dBP
  dBP       dBP           dBP   dBP
 dBP       dBBBBP    dBBBBP'   dBP
<BLANKLINE>
<BLANKLINE>
tsalagi :
 ___    ___      __   ___
 / \     |      /  \  / \
 `  |    |_,  __\___  `  |
    |    | '      \      |
 \_/    _|_    \__/   \_/
<BLANKLINE>
tsn_base :
<BLANKLINE>
 ######   ######    #####   ######
   ##     ##       ##         ##
   ##     #####     ####      ##
   ##     ##           ##     ##
   ##     ######   #####      ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
tubular :
  O~~                     O~~
  O~~                     O~~
O~O~ O~   O~~     O~~~~ O~O~ O~
  O~~   O~   O~~ O~~      O~~
  O~~  O~~~~~ O~~  O~~~   O~~
  O~~  O~            O~~  O~~
   O~~   O~~~~   O~~ O~~   O~~
<BLANKLINE>
<BLANKLINE>
twin_cob :
<BLANKLINE>
#######  #######   ######  #######
   ##    ##       ##          ##
   ##    ######    #####      ##
   ##    ##            ##     ##
   ##    #######  ######      ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
twisted :
  _______      _____   ______    _______
/\_______)\  /\_____\ / ____/\ /\_______)\
\(___  __\/ ( (_____/ ) ) __\/ \(___  __\/
  / / /      \ \__\    \ \ \     / / /
 ( ( (       / /__/_   _\ \ \   ( ( (
  \ \ \     ( (_____\ )____) )   \ \ \
  /_/_/      \/_____/ \____\/    /_/_/
<BLANKLINE>
<BLANKLINE>
twopoint :
_|_ _ __|_
 | }__\ |
<BLANKLINE>
type_set :
  #####   ######    ####     #####
    #     #        #    #      #
    #     #        #           #
    #     ####      ####       #
    #     #             #      #
    #     #        #    #      #
    #     ######    ####       #
<BLANKLINE>
<BLANKLINE>
ucf_fan :
#######  #######    ####   #######
  ###    ###  ##   ### ##    ###
  ###    ###      ###        ###
  ###    ######    #####     ###
  ###    ###           ##    ###
  ###    ###  ##   ##  ##    ###
  ###    #######   #####     ###
<BLANKLINE>
<BLANKLINE>
ugalympi :
<BLANKLINE>
 ######   #####             ######
 # ##     ###               # ##
   #      ####      ####      #
   ##     ##       ###        ##
   ##     ## ##     ####      ##
  ###     #####       ##     ###
                   ####
<BLANKLINE>
unarmed :
 ######  #######   #####    ######
   ##    ##       ##   ##     ##
   ##    ##       ##          ##
   ##    #####     #####      ##
   ##    ##            ##     ##
   ##    ##       ##   ##     ##
   ##    #######   #####      ##
<BLANKLINE>
<BLANKLINE>
univers :
<BLANKLINE>
<BLANKLINE>
  ,d                              ,d
  88                              88
MM88MMM   ,adPPYba,  ,adPPYba,  MM88MMM
  88     a8P_____88  I8[    ""    88
  88     8PP"""""""   `"Y8ba,     88
  88,    "8b,   ,aa  aa    ]8I    88,
  "Y888   `"Ybbd8"'  `"YbbdP"'    "Y888
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
usa :
<BLANKLINE>
<BLANKLINE>
########  #######  ####### ########
   ##                         ##
   ##     ####     #######    ##
   ##     ##            ##    ##
   ##     #######  #######    ##
<BLANKLINE>
<BLANKLINE>
usa_pq :
<BLANKLINE>
<BLANKLINE>
########  #######  ####### ########
   ##                         ##
   ##     ####     #######    ##
   ##     ##            ##    ##
   ##     #######  #######    ##
<BLANKLINE>
<BLANKLINE>
usaflag :
 :::==== :::===== :::===  :::====
 :::==== :::      :::     :::====
   ===   ======    =====    ===
   ===   ===          ===   ===
   ===   ======== ======    ===
<BLANKLINE>
<BLANKLINE>
utopia :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 #             #
###   ##   ## ###
 #   #  # #    #
 #   ####  #   #
 #   #      #  #
  ##  ### ##    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
utopiab :
<BLANKLINE>
<BLANKLINE>
  #                #
 ##               ##
####  ###   #### ####
 ##  ## ## ###    ##
 ##  #####  ###   ##
 ##  ##      ###  ##
  ##  #### ####    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
utopiabi :
<BLANKLINE>
<BLANKLINE>
  #                #
 ##               ##
####   ##    ### ####
 ##   # ##  ##    ##
##   ####   ###  ##
##   ##      ##  ##
###  ####  ###   ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
utopiai :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 #            #
###   ##  ## ###
 #   # # #    #
#   ###   #  #
#   #      # #
##   ##  ##  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
varsity :
  _                      _
 / |_                   / |_
`| |-'  .---.   .--.   `| |-'
 | |   / /__\\ ( (`\]   | |
 | |,  | \__.,  `'.'.   | |,
 \__/   '.__.' [\__) )  \__/
<BLANKLINE>
<BLANKLINE>
vortron :
 #######    #####    ####   #######
   ##      ##       ##  ##    ##
   ##     ##       ##         ##
   ##     ######    #####     ##
   ##     ##            ##    ##
   ##     ##       ##   ##    ##
   ##     #######   #####     ##
<BLANKLINE>
<BLANKLINE>
war_of_w :
<BLANKLINE>
#######  #######    ####   #######
#  ##  #  ##    #  ##      #  ##  #
#####     ####      ####   #####
   ##     ##           ##     ##
   ##     ##    # ##   ##     ##
  ####   #######    #### #   ####
<BLANKLINE>
<BLANKLINE>
wavy :
<BLANKLINE>
_)_   _   _ _)_
(_   )_) (  (_
    (_   _)
<BLANKLINE>
weird :
<BLANKLINE>
 /              /
(___  ___  ___ (___
|    |___)|___ |
|__  |__   __/ |__
<BLANKLINE>
<BLANKLINE>
wetletter :
 _______  ,---.      .---.  _______
|__   __| | .-'     ( .-._)|__   __|
  )| |    | `-.    (_) \     )| |
 (_) |    | .-'    _  \ \   (_) |
   | |    |  `--. ( `-'  )    | |
   `-'    /( __.'  `----'     `-'
         (__)
<BLANKLINE>
whimsy :
<BLANKLINE>
   d8P                    d8P
d888888P               d888888P
  ?88'   d8888b .d888b,  ?88'
  88P   d8b_,dP ?8b,     88P
  88b   88b       `?8b   88b
  `?8b  `?888P'`?888P'   `?8b
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
wow :
`][` ]E ((5 `][`
<BLANKLINE>
xbrite :
<BLANKLINE>
<BLANKLINE>
 #             #
 #             #
 #             #
###  ##  #### ###
 #  #  # #  #  #
 #  ####  ##   #
 #  #    #  #  #
 ##  ### ####  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xbriteb :
<BLANKLINE>
 #              #
 #              #
 #              #
###  ###  #### ###
 #  #   # #  #  #
 #  #####  ##   #
 #  #     #  #  #
 ##  #### ####  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xbritebi :
<BLANKLINE>
  #              #
 ##             ##
 ##             ##
####   ## #### ####
 #    # #  # #  #
 #   ###   ##   #
#    #  # #  # #
###  #### ###  ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xbritei :
<BLANKLINE>
<BLANKLINE>
 #             #
 #             #
 #             #
###   ##  ### ###
 #   # #  # #  #
#   ###   ##  #
#   #  # # #  #
##  #### ###  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xchartr :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
#           #
##  ##   ## ##
#  #  # # # #
#  ####  #  #
#  #    # # #
##  ### ##  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xchartri :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 #             #
###   #    ## ###
#    ###  #   #
#   ##     #  #
#   #  # #  # #
##   ##   ##  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xcour :
<BLANKLINE>
<BLANKLINE>
 #               #
 #               #
####   ##   ### ####
 #    #  # #     #
 #    ###   ##   #
 #  # #       #  #  #
  ##   ### ###    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xcourb :
<BLANKLINE>
<BLANKLINE>
 ##                  ##
 ##                  ##
#####   ###   ####  #####
 ##    ## ## ###     ##
 ##    #####  ####   ##
 ## ## ##       ###  ## ##
  ###   #### #####    ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xcourbi :
<BLANKLINE>
<BLANKLINE>
  ##                  ##
 ##                  ##
#####   ###    #### #####
 ##    ## ##  ##  #  ##
##    ######  ####  ##
## ## ##     #  ##  ## ##
 ###   ####  ####    ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xcouri :
<BLANKLINE>
<BLANKLINE>
   #                 #
  #                 #
#####   ##    ### #####
  #    #  #  #      #
 #    #####   ##   #
 #  # #     #  #   #  #
  ##   ###  ###     ##
xhelv :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 #             #
 #             #
###  ##   ##  ###
 #  #  # #  #  #
 #  ####  ##   #
 #  #       #  #
 #  #  # #  #  #
 ##  ##   ##   ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xhelvb :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
##              ##
##              ##
###  ###   ###  ###
##  ## ## ## ## ##
##  #####  ###  ##
##  ##       ## ##
##  ## ## ## ## ##
 ##  ###   ###   ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xhelvbi :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
  ##                  ##
  ##                  ##
#####   ###    ###  #####
 ##    ## ##  ## ##  ##
 ##    #####   ##    ##
##    ##        ##  ##
##    ## ##  ## ##  ##
 ##    ###    ###    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xhelvi :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
  #                #
  #                #
####   ###   ##  ####
 #    #  #  #  #  #
 #   ####    #    #
#    #        #  #
#    #  #  #  #  #
##    ##    ##   ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xsans :
<BLANKLINE>
<BLANKLINE>
 #             #
####  ##   ## ####
 #   #  # #    #
 #   #### ##   #
 #   #     ##  #
 #   #      #  #
  ##  ### ##    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xsansb :
<BLANKLINE>
<BLANKLINE>
 ##              ##
####  ###   ### ####
 ##  ## ## ##    ##
 ##  ## ## ###   ##
 ##  #####  ###  ##
 ##  ##      ##  ##
  ##  #### ###    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xsansbi :
<BLANKLINE>
<BLANKLINE>
  ##                 ##
#####   ###   #### #####
 ##    # ##  ##     ##
 ##   ## ##  ###    ##
##    ####    ###  ##
##    ##       ##  ##
###    ###  ####   ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xsansi :
<BLANKLINE>
<BLANKLINE>
  #                #
####   ###   ### ####
 #    #  #  #     #
 #   #  #   ##    #
#    ###     ##  #
#    #        #  #
##    ###  ###   ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xtimes :
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
 #            #
##           ##
###  ###  ## ###
##  ## # ##  ##
##  #### ### ##
##  ##    ## ##
 ##  ### ###  ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xtty :
<BLANKLINE>
<BLANKLINE>
 #              #
####  ##   ### ####
 #   #  # #     #
 #   #### ##    #
 #   #      ##  #
 #   #       #  #
  ##  ### ###    ##
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
xttyb :
<BLANKLINE>
<BLANKLINE>
 ##                ##
#####  ###   #### #####
 ##   ## ## ##     ##
 ##   ## ## ####   ##
 ##   #####  ####  ##
 ##   ##       ##  ##
  ###  #### ####    ###
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
yie-ar :
 ######   ######    ####    ######
######   ####      ### ##  ######
   ##    ###       ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     ###     ### ###     ##
    #      #####   #####       #
<BLANKLINE>
<BLANKLINE>
yie_ar_k :
 ######   ######    ####    ######
######   ####      ### ##  ######
   ##     #        ##         ##
   ##     ####      ####      ##
   ##     ##           ##     ##
   ##     ###     ### ###     ##
    #      #####   #####       #
<BLANKLINE>
<BLANKLINE>
z-pilot :
######   ## ###    ####    ######
######   ## ###   ######   ######
         ##       ##  ##
  ##     ## ###    ##        ##
  ##     ## ###      ##      ##
  ##     ##       ##  ##     ##
  ##     ## ###   ######     ##
  ##     ## ###    ####      ##
<BLANKLINE>
zig_zag :
 ######  #######   #####    ######
 ######  #######  #######   ######
 # ## #  ##   #   ###  ##   # ## #
   ##    ####      ###        ##
   ##    ##          ###      ##
   ##    ##   ##  ##  ###     ##
   ##    #######   #####      ##
<BLANKLINE>
<BLANKLINE>
zone7 :
 ######  #######   #####    ######
 ######  #######  #######   ######
<BLANKLINE>
   ##    ####     ######      ##
   ##    ##            ##     ##
   ##    #######  #######     ##
   ##    #######   #####      ##
<BLANKLINE>
<BLANKLINE>
>>> tprint("test",font = "block243")
<BLANKLINE>
  _|                            _|
_|_|_|_|    _|_|      _|_|_|  _|_|_|_|
  _|      _|_|_|_|  _|_|        _|
  _|      _|            _|_|    _|
    _|_|    _|_|_|  _|_|_|        _|_|
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
>>> tprint("test1\ntest2\ntest3")
 _               _    _
| |_   ___  ___ | |_ / |
| __| / _ \/ __|| __|| |
| |_ |  __/\__ \| |_ | |
 \__| \___||___/ \__||_|
<BLANKLINE>
 _               _    ____
| |_   ___  ___ | |_ |___ \
| __| / _ \/ __|| __|  __) |
| |_ |  __/\__ \| |_  / __/
 \__| \___||___/ \__||_____|
<BLANKLINE>
 _               _    _____
| |_   ___  ___ | |_ |___ /
| __| / _ \/ __|| __|  |_ \
| |_ |  __/\__ \| |_  ___) |
 \__| \___||___/ \__||____/
<BLANKLINE>
<BLANKLINE>
>>> tprint("\n\ntest1\ntest2\ntest3")
<BLANKLINE>
<BLANKLINE>
 _               _    _
| |_   ___  ___ | |_ / |
| __| / _ \/ __|| __|| |
| |_ |  __/\__ \| |_ | |
 \__| \___||___/ \__||_|
<BLANKLINE>
 _               _    ____
| |_   ___  ___ | |_ |___ \
| __| / _ \/ __|| __|  __) |
| |_ |  __/\__ \| |_  / __/
 \__| \___||___/ \__||_____|
<BLANKLINE>
 _               _    _____
| |_   ___  ___ | |_ |___ /
| __| / _ \/ __|| __|  |_ \
| |_ |  __/\__ \| |_  ___) |
 \__| \___||___/ \__||____/
<BLANKLINE>
<BLANKLINE>
>>> tprint("\n\ntest1\ntest2\ntest3\n\n\n",font="lcd")
<BLANKLINE>
<BLANKLINE>
                         _
  |                 |     |
 -+-   -       -   -+-    +
  |   |/       \    |     |
   -   --      -     -   ---
<BLANKLINE>
                         ___
  |                 |       |
 -+-   -       -   -+-   -+-
  |   |/       \    |   |
   -   --      -     -   ---
<BLANKLINE>
                         ___
  |                 |       |
 -+-   -       -   -+-   -+-
  |   |/       \    |       |
   -   --      -     -   ---
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
>>> tprint("",font="lcd")
<BLANKLINE>
>>> tprint("\n",font="lcd")
<BLANKLINE>
<BLANKLINE>
>>> tprint("\n\n",font="lcd")
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
>>> text2art("")
''
>>> text2art("",font="lcd")
''
>>> art_list(mode="ascii")
alien
::)
******************************
american money1
[($)]
******************************
angel1
^i^
******************************
angel2
O:-)
******************************
arrow2
XXX-------->
******************************
atish
(| - _ - |)
******************************
awesome
<:3 )~~~
******************************
bad hair1
=:-)
******************************
bad hair2
=:-(
******************************
bagel
nln >_< nln
******************************
baseball fan
q:o)
******************************
bat1
^O^
******************************
bat2
 ^v^
******************************
big kiss
:-X
******************************
big smile
:-D
******************************
blackeye
0__#
******************************
boobies
(. )( .)
******************************
boobs
(.)(.)
******************************
buck teeth
:-B
******************************
bugs bunny
E=B
******************************
bunny
(\_/)
******************************
canoe
.,.,\______/,..,.,
******************************
car
`o##o>
******************************
care crowd
(-(-_(-_-)_-)-)
******************************
carpet roll
@__
******************************
cat1
=^..^=
******************************
cat3
^.--.^
******************************
caterpillar
,/\,/\,/\,/\,/\,/\,o
******************************
charly
+:)
******************************
cigarette2
(____((____________()~~~
******************************
cigarette3
()___)____________)
******************************
clowning
*:o)
******************************
coffee1
c[_]
******************************
coffee2
l_D
******************************
coffee3
l_P
******************************
coffee4
l_B
******************************
computer mouse
[E}
******************************
concerned
(@_@)
******************************
confused1
:-/
******************************
confused10
(*'__'*)
******************************
confused2
:-\
******************************
confused4
^^'
******************************
crab
(\|) ._. (|/)
******************************
crotch shot
\*/
******************************
crying2
:~(
******************************
cthulhu
^(;,;)^
******************************
cthulhu2
( ;,;)
******************************
cussing
:-#
******************************
dagger
cxxx|;:;:;:;:;:;:;:;>
******************************
dance
(>'-')> <('_'<) ^('_')\- \m/(-_-)\m/ <( '-')> \_( .")> <(._.)-`
******************************
dead child
'-=,o
******************************
dead girl
'==>x\9
******************************
dead guy
'==xx\0
******************************
death star defense team
|-o-| (-o-) |-o-|
******************************
devil
]:->
******************************
devilish grin
>:-D
******************************
devilish smile
>:)
******************************
dice
[: :]
******************************
dick
8====D
******************************
dna sample
~
******************************
domino
[: :|:::]
******************************
don king
==8-)
******************************
druling1
:-...
******************************
druling2
:-P~~~
******************************
dude glasses1
@[O],[O]
******************************
dude glasses2
@(o),(o)
******************************
dummy
<-|-'_'-|->
******************************
eastbound fish
><((((>
******************************
eaten apple
[===]-'
******************************
eds dick
8=D
******************************
eeriemob
(-(-_-(-_(-_(-_-)_-)-_-)_-)_-)-)
******************************
emo
(///_ ;)
******************************
eric
>--) ) ) )*>
******************************
fat ass
(__!__)
******************************
faydre
(U) [^_^] (U)
******************************
fish skeleton1
>-}-}-}->
******************************
fish skeleton2
>++('>
******************************
fish1
><(((('>
******************************
fish2
><>
******************************
fish4
><>     ><>
******************************
fish5
<><
******************************
fish6
<`)))><
******************************
fork
---=
******************************
fox
-^^,--,~
******************************
fuck off
t(-.-t)
******************************
fuck you
nlm (-_-) mln
******************************
full mouth
:-I
******************************
glasses
-@-@-
******************************
hacksaw
[|^^^^^^^
******************************
hal
@_'-'
******************************
hammer
#==
******************************
happy4
^_^
******************************
happy5
[^_^]
******************************
headphone1
d[-_-]b
******************************
headphone2
d(-_-)b
******************************
headphone3
(W)
******************************
heart3
<3
******************************
homer
(_8(|)
******************************
homer simpson
=(:o)
******************************
hoxom
h(o x o )m
******************************
infinity
(X)
******************************
joy
n_n
******************************
king
-_-
******************************
kirby dance
<(''<)  <( ' ' )>  (> '')>
******************************
kiss
(o'3'o)
******************************
kiss my ass
(_x_)
******************************
kitty
=^. .^=
******************************
knife1
)xxxxx[;;;;;;;;;>
******************************
knife2
)xxx[::::::::::>
******************************
koala
@( * O * )@
******************************
licking lips
:-9
******************************
linqan
:Q___
******************************
loch ness monster
_mmmP
******************************
long rose
---------------------{{---<((@)
******************************
looking down
(._.)
******************************
machinegun
,==,--
******************************
mad3
>_<
******************************
mad4
~_~
******************************
mad8
{{{(>_<)}}}
******************************
mail box
|M|/
******************************
marge simpson
()()():|
******************************
marshmallows
-()_)--()_)---
******************************
melp1
(<>..<>)
******************************
melp2
(<(<>(<>.(<>..<>).<>)<>)>)
******************************
metal
\m/_(>_<)_\m/
******************************
mini penis
=D
******************************
monkey
@('_')@
******************************
mouse1
----{,_,">
******************************
mouse5
<:3 )~~~~
******************************
mouse6
<^__)~
******************************
mouse7
~(__^>
******************************
mtmtika
:o + :p = 69
******************************
needle2
|==|iiii|>-----
******************************
nerd
::(
******************************
nope
t(-_-t)
******************************
nose2
|'L'|
******************************
oar
===========(8888)
******************************
old lady boobs
|\o/\o/|
******************************
palm tree
'T`
******************************
penis
8===D
******************************
pictou
|\_______(#*#)_______/|
******************************
pie fight
---=======[}
******************************
pig1
^(*(oo)*)^
******************************
pipe
====\_/
******************************
possessed
<>_<>
******************************
power lines
TTT
******************************
punch
O=('-'Q)
******************************
pursing lips
:-"
******************************
ready to cry
:-}
******************************
really mad
>:-I
******************************
really sad
:-C
******************************
regular ass
(_!_)
******************************
roadblock
X+X+X+X+X
******************************
robot1
d[ o_0 ]b
******************************
rock on1
\,,/(^_^)\,,/
******************************
rock on2
\m/(-_-)\m/
******************************
roke
_\m/
******************************
rose1
--------{---(@
******************************
rose2
@}}>-----
******************************
rose3
@-->--->---
******************************
rose4
@}~}~~~
******************************
rose5
@-}--
******************************
rose6
@)}---^-----
******************************
rose7
@->-->---
******************************
round cat
~(^._.)
******************************
russian boobs
[.][.]
******************************
ryans dick
8======D
******************************
sad6
Y_Y
******************************
screaming
:-@
******************************
sean the sheep
<('--')>
******************************
shark
~~~~~~^~~~~~
******************************
shark attack
~~~~~~\o/~~~~~/\~~~~~
******************************
shocked2
:-O
******************************
sleeping
(-.-)Zzz...
******************************
sleeping baby
[{-_-}] ZZZzz zz z...
******************************
smile
:-)
******************************
smirk
:-,
******************************
snail1
'-'_@_
******************************
snail2
'\Q___
******************************
snowman2
{ }( : ^ )( """" )( )
******************************
sophie
<XX""XX>
******************************
spear
>>-;;;------;;-->
******************************
sperm
~~o
******************************
spider1
//O\
******************************
spider2
/\oo/\
******************************
spider3
///\oo/\\\
******************************
spot
(  . Y .  )
******************************
squigle with spirals
6\9
******************************
star in my eyes
<*_*>
******************************
stealth fighter
-^-
******************************
sunglasses2
B-)
******************************
superman
-^mOm^-
******************************
superman logo
/s\
******************************
surprised1
=:-o
******************************
surprised14
(O.O)
******************************
surprised3
(O_o)
******************************
surprised7
O.o
******************************
sword1
(===||:::::::::::::::>
******************************
sword10
O==I======>
******************************
sword4
 |O/////[{:;:;:;:;:;:;:;:;>
******************************
sword5
<%%%%|==========>
******************************
sword6
o()xxxx[{::::::::::::::::::::::::::::::::::>
******************************
sword7
o==[]::::::::::::::::>
******************************
teepee
/|\
******************************
tent1
//\
******************************
tent2
/\\
******************************
thanks
\(^-^)/
******************************
this is areku
d(^o^)b
******************************
toungue out2
:-P
******************************
tree stump
J"l
******************************
tron
(\/)(;,,;)(\/)
******************************
trumpet
-=iii=<()
******************************
ufo1
.-=-.
******************************
ufo2
.-=o=-.
******************************
ukulele
{ o }==(::)
******************************
vagina
(:)
******************************
victory
V(-.o)V
******************************
volcano1
/"\
******************************
volcano2
/W\
******************************
volcano3
/V\
******************************
wave dance
~(^-^)~
******************************
westbound fish
< )))) ><
******************************
wink
;-)
******************************
woops
:-*
******************************
worm
_/\__/\__0>
******************************
yo
__o000o__(o)(o)__o000o__
******************************
>>> aprint(artname = "awesame")
<:3 )~~~
>>> aprint(artname = "awesame", number=2)
<:3 )~~~ <:3 )~~~
>>> aprint(artname = "awesame", number=2, space=5)
<:3 )~~~     <:3 )~~~
>>> help_func()
              _
  __ _  _ __ | |_
 / _` || '__|| __|
| (_| || |   | |_
 \__,_||_|    \__|
<BLANKLINE>
<BLANKLINE>
         __       ____
__   __ / /_     | ___|
\ \ / /| '_ \    |___ \
 \ V / | (_) | _  ___) |
  \_/   \___/ (_)|____/
<BLANKLINE>
<BLANKLINE>
ASCII art is also known as "computer text art".
It involves the smart placement of typed special characters or
letters to make a visual shape that is spread over multiple lines of text.
ART is a Python lib for text converting to ASCII art fancy.
<BLANKLINE>
Webpage : https://www.ascii-art.site
<BLANKLINE>
Help :
<BLANKLINE>
     - list --> (list of arts)
<BLANKLINE>
     - fonts --> (list of fonts)
<BLANKLINE>
     - test --> (run tests)
<BLANKLINE>
     - text [yourtext] [font(optional)] --> (text art) Example : 'art text exampletext block'
<BLANKLINE>
     - shape [shapename] --> (shape art) Example : 'art shape butterfly'
<BLANKLINE>
     - save [yourtext] [font(optional)]  -->  Example : 'art save exampletext block'
<BLANKLINE>
     - all [yourtext]  -->  Example : 'art all exampletext'
<BLANKLINE>
>>> tprint('طط')
<BLANKLINE>
>>> art1 = "MonSter"
>>> art1_copy = "MonSter"
>>> Art0 = art(art1)
>>> art1 == art1_copy
True
>>> random.seed(3)
>>> Art = art("random")
>>> random.seed(40)
>>> Text = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","random")
>>> random.seed(55)
>>> Art3 = randart()
>>> random.seed(19)
>>> Text2 = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","random")
>>> random.seed(39)
>>> Art2 =  art("random")
>>> random.seed(119)
>>> Art4 = randart()
>>> Art == Art2
False
>>> Text == Text2
False
>>> Art3 == Art4
False
>>> Data=art('assdsds')
Traceback (most recent call last):
        ...
art.art.artError: Invalid art name.
>>> art("coffee")
'c[_]'
>>> art("coffee", number=2)
'c[_] c[_]'
>>> art("coffee", number=2, space=5)
'c[_]     c[_]'
>>> tprint("test 2")
 _               _     ____
| |_   ___  ___ | |_  |___ \
| __| / _ \/ __|| __|   __) |
| |_ |  __/\__ \| |_   / __/
 \__| \___||___/ \__| |_____|
<BLANKLINE>
<BLANKLINE>
>>> tprint("aasdasdال",chr_ignore=True)
                        _                  _
  __ _   __ _  ___   __| |  __ _  ___   __| |
 / _` | / _` |/ __| / _` | / _` |/ __| / _` |
| (_| || (_| |\__ \| (_| || (_| |\__ \| (_| |
 \__,_| \__,_||___/ \__,_| \__,_||___/ \__,_|
<BLANKLINE>
<BLANKLINE>
>>> tprint("$2","block")
<BLANKLINE>
 .----------------.
| .--------------. |
| |    _____     | |
| |   / ___ `.   | |
| |  |_/___) |   | |
| |   .'____.'   | |
| |  / /____     | |
| |  |_______|   | |
| |              | |
| '--------------' |
 '----------------'
<BLANKLINE>
>>> tprint("salam\t","lcd")
<BLANKLINE>
              |
   -   -      +    -    |- -
   \  | |     |   | |   | | |
   -   --     -    --
<BLANKLINE>
<BLANKLINE>
>>> text2art("test",font = 2)
Traceback (most recent call last):
        ...
art.art.artError: The 'font' type must be str.
>>> Data=tsave("test file\nk",filename="test")
Saved!
Filename: test.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data=tsave("test file\nk",filename="test.bw")
Saved!
Filename: test.bw
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data=tsave("test art")
Saved!
Filename: art.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data=tsave("test art2")
Saved!
Filename: art2.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data=tsave("test art3",print_status=False)
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> file=open("test.txt","r")
>>> data = file.read()
>>> print(data)
 _               _      __  _  _
| |_   ___  ___ | |_   / _|(_)| |  ___
| __| / _ \/ __|| __| | |_ | || | / _ \
| |_ |  __/\__ \| |_  |  _|| || ||  __/
\__| \___||___/ \__| |_|  |_||_| \___|
<BLANKLINE>
 _
| | __
| |/ /
|   <
|_|\_\
<BLANKLINE>
<BLANKLINE>
>>> len(data)==282
True
>>> file=open("art.txt","r")
>>> data = file.read()
>>> print(data)
 _               _                  _
| |_   ___  ___ | |_    __ _  _ __ | |_
| __| / _ \/ __|| __|  / _` || '__|| __|
| |_ |  __/\__ \| |_  | (_| || |   | |_
\__| \___||___/ \__|  \__,_||_|    \__|
<BLANKLINE>
<BLANKLINE>
>>> len(data)==246
True
>>> file=open("art2.txt","r")
>>> data = file.read()
>>> print(data)
 _               _                  _    ____
| |_   ___  ___ | |_    __ _  _ __ | |_ |___ \
| __| / _ \/ __|| __|  / _` || '__|| __|  __) |
| |_ |  __/\__ \| |_  | (_| || |   | |_  / __/
\__| \___||___/ \__|  \__,_||_|    \__||_____|
<BLANKLINE>
<BLANKLINE>
>>> len(data)==288
True
>>> file.close()
>>> Data=text2art(222)
Traceback (most recent call last):
        ...
art.art.artError: The 'text' type must be str.
>>> text2art("seسسس",chr_ignore=False)
Traceback (most recent call last):
        ...
art.art.artError: س is invalid.
>>> Data=tsave(22,filename="art",chr_ignore=True,print_status=True)
>>> Data["Message"]
"The 'text' type must be str."
>>> Data["Status"]
False
>>> tprint(22,font = DEFAULT_FONT,chr_ignore=True)
Traceback (most recent call last):
        ...
art.art.artError: The 'text' type must be str.
>>> art(22,number=1)
Traceback (most recent call last):
        ...
art.art.artError: The 'artname' type must be str.
>>> art("woman",space="22")
Traceback (most recent call last):
        ...
art.art.artError: The 'space' type must be int.
>>> aprint("woman",number="22")
Traceback (most recent call last):
        ...
art.art.artError: The 'number' type must be int.
>>> aprint("woman",space="22")
Traceback (most recent call last):
        ...
art.art.artError: The 'space' type must be int.
>>> set_default(font="italic")
>>> tprint("test")
<BLANKLINE>
_/  _   _ _/
/  (- _)  /
<BLANKLINE>
<BLANKLINE>
>>> set_default(font=2)
Traceback (most recent call last):
        ...
art.art.artError: The 'font' type must be str.
>>> set_default(chr_ignore=2)
Traceback (most recent call last):
        ...
art.art.artError: The 'chr_ignore' type must be bool.
>>> set_default(filename=2)
Traceback (most recent call last):
        ...
art.art.artError: The 'filename' type must be str.
>>> set_default(print_status=2)
Traceback (most recent call last):
        ...
art.art.artError: The 'print_status' type must be bool.
>>> set_default(overwrite=2)
Traceback (most recent call last):
        ...
art.art.artError: The 'overwrite' type must be bool.
>>> set_default(sep=True)
Traceback (most recent call last):
        ...
art.art.artError: The 'sep' type must be str.
>>> set_default(decoration=2)
Traceback (most recent call last):
        ...
art.art.artError: The 'decoration' type must be str.
>>> set_default(space='test')
Traceback (most recent call last):
        ...
art.art.artError: The 'space' type must be int.
>>> set_default(__detailed_return='test')
Traceback (most recent call last):
        ...
art.art.artError: The '__detailed_return' type must be bool.
>>> random.seed(200)
>>> Art = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-small")
>>> random.seed(800)
>>> Art2 = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-small")
>>> Art == Art2
False
>>> random.seed(200)
>>> Art = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-medium")
>>> random.seed(800)
>>> Art2 = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-medium")
>>> Art == Art2
False
>>> random.seed(200)
>>> Art = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-large")
>>> random.seed(800)
>>> Art2 = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-large")
>>> Art == Art2
False
>>> random.seed(200)
>>> Art = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-xlarge")
>>> random.seed(800)
>>> Art2 = text2art("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~0123456789","rnd-xlarge")
>>> Art == Art2
False
>>> Art = text2art("te","wizard")
>>> Art2 = text2art("test","wizard")
>>> Art3 = text2art("test"*2,"wizard")
>>> Art4 = text2art("test"*5,"wizard")
>>> Art == Art2
False
>>> Art == Art3
False
>>> Art == Art4
False
>>> text1 = "test"
>>> text1_copy = "test"
>>> font1 = "Beer_Pub"
>>> font1_copy = "Beer_Pub"
>>> Art5 = text2art(text1,font1)
>>> text1 == text1_copy
True
>>> font1 == font1_copy
True
>>> text2 = "TEST"
>>> text2_copy = "TEST"
>>> Art6 = text2art(text2,"double")
>>> text2 == text2_copy
True
>>> from art.utils import font_size_splitter
>>> from art.params import FONT_MAP,RANDOM_FILTERED_FONTS
>>> font_dicts = font_size_splitter(FONT_MAP)
>>> len(font_dicts["small_list"])>0
True
>>> len(font_dicts["medium_list"])>0
True
>>> len(font_dicts["large_list"])>0
True
>>> len(font_dicts["xlarge_list"])>0
True
>>> (len(font_dicts["small_list"]) + len(font_dicts["medium_list"]) + len(font_dicts["large_list"]) + len(font_dicts["xlarge_list"])) == (FONT_COUNTER - len(RANDOM_FILTERED_FONTS))
True
>>> for font in FONT_MAP:
...     for letter in string.ascii_letters + string.punctuation + string.digits + " ":
...         Data = text2art(letter,font,chr_ignore=False)
>>> file = open("art.txt","r")
>>> print(file.read())
 _               _                  _
| |_   ___  ___ | |_    __ _  _ __ | |_
| __| / _ \/ __|| __|  / _` || '__|| __|
| |_ |  __/\__ \| |_  | (_| || |   | |_
 \__| \___||___/ \__|  \__,_||_|    \__|
<BLANKLINE>
<BLANKLINE>
>>> file.close()
>>> Data = tsave("test","standard",filename="test1.txt")
Saved!
Filename: test1.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data = tsave("test","standard",filename="test1.txt", overwrite=True)
Saved!
Filename: test1.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data = tsave("test","standard",filename="test1.2.txt")
Saved!
Filename: test1.2.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> result = []
>>> for i in FONT_NAMES:
...	    t = tsave("save-test",font=i,filename="save_test.txt",print_status=False,overwrite=True)
...	    result.append(t["Status"])
>>> all(result)
True
>>> os.remove("art.txt")
>>> os.remove("art2.txt")
>>> os.remove("art3.txt")
>>> os.remove("test.bw")
>>> os.remove("test.txt")
>>> os.remove("test1.txt")
>>> os.remove("test1.2.txt")
>>> os.remove("save_test.txt")

'''
