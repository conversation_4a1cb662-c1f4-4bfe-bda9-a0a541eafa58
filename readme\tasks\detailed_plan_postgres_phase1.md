# Detailed Plan: BMS Charging Data Processing - Phase 1 (PostgreSQL)

This document outlines the detailed plan for implementing Phase 1 of the BMS (Battery Management System) charging data processing pipeline, focusing on setting up a PostgreSQL database and the initial data ingestion scripts.

## 1. Overall Goal

Transition the BMS data processing pipeline to use PostgreSQL as the backend database, starting with the creation of core tables and data ingestion mechanisms for BMS charging data. This phase focuses on establishing the foundation for PostgreSQL integration.

## 2. Key Objectives from Initial Plan

*   **PostgreSQL Setup:**
    *   DB Name: `postgres`
    *   User: `postgres` (initially, can be refined)
    *   Password: `postgres` (to be stored in `.env`)
    *   Table Name: `bms_charging_data` (primary focus for Phase 1 ingestion)
    *   Host: `localhost`
    *   Port: `5999`
*   **Configuration Management:** Utilize a `.env` file for database connection credentials.
*   **DB Management Scripts:** Develop new Python scripts within a dedicated `code/db_operations/` directory for PostgreSQL.
    *   Script to create the `bms_charging_data` table.
    *   Script to upload data from CSV files into `bms_charging_data`.
*   **Data Processing Placeholders:** Create placeholder scripts in `code/bms_data_processing/` for future development.
*   **Libraries:**
    *   `loguru` for logging.
    *   `polars` for CSV processing and data manipulation (replacing `pandas`).
    *   `psycopg2-binary` (or `psycopg`) for PostgreSQL connection.
    *   `python-dotenv` for managing environment variables.

## 3. Detailed Implementation Steps

### Step 3.1: Environment Setup & Dependencies

*   **Create/Update `.env` file:**
    *   This file will store the database connection details.
    *   Example content:
      ```env
      DB_HOST=localhost
      DB_PORT=5999
      DB_NAME=postgres
      DB_USER=postgres
      DB_PASSWORD=postgres
      ```
*   **Update Project Dependencies:**
    *   Ensure the following libraries are listed in `pyproject.toml` (if using Poetry/PDM) or a `requirements.txt` file:
        *   `psycopg2-binary` (or `psycopg`)
        *   `python-dotenv`
        *   `loguru`
        *   `polars`
    *   Install/update dependencies as needed.

### Step 3.2: Directory Structure

Create the following new directories if they don't exist:

*   `code/db_operations/`: For PostgreSQL specific database management scripts.
*   `code/bms_data_processing/`: For scripts related to BMS data transformation and analysis (placeholders in Phase 1).
*   `input/bms_csv_data/`: This directory will serve as the source for CSV files to be uploaded by the new PostgreSQL upload script.

### Step 3.3: PostgreSQL DB Management Scripts (in `code/db_operations/`)

#### 3.3.1. `pg_db_utils.py` (Optional but Recommended)

*   **Purpose:** Centralize common database connection logic and logging setup.
*   **Functionality:**
    *   Function to establish a PostgreSQL connection using credentials from the `.env` file.
    *   Reusable `loguru` logger configuration.
*   **Libraries:** `psycopg2-binary`, `python-dotenv`, `loguru`, `os`.

#### 3.3.2. `pg_create_bms_table.py`

*   **Purpose:** Creates the `bms_charging_data` table in the PostgreSQL database.
*   **Functionality:**
    *   Imports connection utilities from `pg_db_utils.py` (if created) or establishes a direct connection using `.env` variables.
    *   Defines the SQL `CREATE TABLE` statement for `bms_charging_data` with the schema specified in [`readme/tasks/plan_postgres_bms_phase1.md`](readme/tasks/plan_postgres_bms_phase1.md:16-88).
        *   `id` (SERIAL PRIMARY KEY or appropriate auto-incrementing integer for PostgreSQL)
        *   `bms_vehicle_identification_number` (VARCHAR)
        *   ... (all other columns as specified, ensuring correct PostgreSQL data types: VARCHAR for strings, REAL or DOUBLE PRECISION for float, INTEGER for integer, TIMESTAMP for timestamps)
    *   Executes the `CREATE TABLE IF NOT EXISTS` statement.
    *   Uses `loguru` for logging success or errors.
*   **Libraries:** `psycopg2-binary`, `python-dotenv`, `loguru`.

#### 3.3.3. `pg_upload_bms_data.py`

*   **Purpose:** Reads BMS data from CSV files and uploads it to the `bms_charging_data` table.
*   **Functionality:**
    *   Imports connection utilities from `pg_db_utils.py` (if created) or establishes a direct connection using `.env` variables.
    *   Scans the `input/bms_csv_data/` directory for `.csv` files.
    *   For each CSV file:
        *   Uses `polars` to read the CSV into a DataFrame.
        *   **Data Cleansing (as per original plan for raw script):**
            *   Drop rows where the timestamp column (`ts_bms`) is null.
            *   Select only the columns relevant to the `bms_charging_data` table schema (ensure column names in CSV match or are mapped to table column names).
        *   Constructs and executes SQL `INSERT` statements (consider using `COPY` for better performance with `polars` if feasible, or batch inserts).
        *   Handles potential errors during file processing or database insertion.
        *   Uses `loguru` for logging progress, number of rows inserted, and any errors.
*   **Libraries:** `psycopg2-binary`, `python-dotenv`, `loguru`, `polars`, `os`.

### Step 3.4: BMS Data Processing Scripts (Placeholders in `code/bms_data_processing/`)

Create empty placeholder Python files for future development:

*   **`store_raw_bms_data.py`:**
    *   Add a brief comment: `# Placeholder for Phase 2: Script to process and store raw BMS data into bms_charging_data_raw table.`
    *   This script will eventually handle the `bms_charging_data_raw` table and the initial processing step mentioned in the original plan (dropping columns, handling null timestamps before storing to a "raw" table).
*   **`charging_data_processing.py`:**
    *   Add a brief comment: `# Placeholder for Phase 2: Script to perform advanced processing on BMS charging data from bms_charging_data_raw and store to bms_charging_data.`

### Step 3.5: Documentation & Integration Plan

*   **In-script Comments:** Add clear comments to all new scripts explaining their purpose, key functions, and any important logic.
*   **Future Integration with `run_all.py`:**
    *   The new scripts (`pg_create_bms_table.py`, `pg_upload_bms_data.py`) will need to be callable or integrated into the main execution flow, likely via `run_all.py`.
    *   This might involve adding new command-line arguments or functions to `run_all.py` to trigger these PostgreSQL-specific operations. This integration itself is likely a follow-up task after Phase 1 scripts are functional.

## 4. Workflow Diagram

```mermaid
graph TD
    A[Start: Phase 1 Execution] --> B{Load .env};
    B --> C[/Create Directories/];
    C --> D[code/db_operations/pg_create_bms_table.py];
    D -- Creates Table --> E((PostgreSQL DB: bms_charging_data));
    C --> F[input/bms_csv_data/];
    F -- Contains CSVs --> G[code/db_operations/pg_upload_bms_data.py];
    G -- Reads CSVs (polars) --> G;
    G -- Cleanses Data --> G;
    G -- Uploads Data --> E;
    B -- Provides Credentials --> D;
    B -- Provides Credentials --> G;
    C --> H[/Create Placeholder Scripts/];
    H --> I[code/bms_data_processing/store_raw_bms_data.py];
    H --> J[code/bms_data_processing/charging_data_processing.py];
    K[Loguru Logging] --> D;
    K --> G;
    L[End: Phase 1 Foundation Ready]
    E --> L;
    I --> L;
    J --> L;
```

## 5. Future Considerations (Post-Phase 1)

*   **Refactor Existing MySQL Scripts:** Plan the migration of [`code/db_upload.py`](code/db_upload.py) and [`code/db_delete_data.py`](code/db_delete_data.py) from MySQL to PostgreSQL. This might involve adapting their logic to use the new PostgreSQL utilities and `.env` configuration.
*   **Implement `bms_charging_data_raw`:** Fully implement `store_raw_bms_data.py` to populate the `bms_charging_data_raw` table as per the original plan.
*   **Implement Phase 2 Processing:** Develop the logic within `charging_data_processing.py`.
*   **Error Handling & Resilience:** Enhance error handling, add retries, and improve data validation in the upload scripts.
*   **Testing:** Implement unit and integration tests for the new scripts.
*   **Security:** Review database user permissions and secure credential management beyond the initial `.env` setup for production environments.

This detailed plan should guide the development efforts for Phase 1.