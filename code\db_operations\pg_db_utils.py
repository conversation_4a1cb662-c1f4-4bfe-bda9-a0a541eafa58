import os
import psycopg2
from dotenv import load_dotenv
from loguru import logger
import sys

load_dotenv()  # Load environment variables from .env file

DB_HOST = os.environ.get('DB_HOST')
DB_PORT = os.environ.get('DB_PORT')
DB_NAME = os.environ.get('DB_NAME')
DB_USER = os.environ.get('DB_USER')
DB_PASSWORD = os.environ.get('DB_PASSWORD')

# Shared list of BMS data columns (excluding 'id' which is SERIAL PRIMARY KEY)
BMS_DATA_COLUMNS = [
    "bms_vehicle_identification_number", "bms_scooter_state", "kwhr_charged_discharged_mode",
    "total_regen_amhr", "amhr_accumulated_current_cycle", "balancing_temp_pdu",
    "battery_current", "battery_pack_effective_temp", "battery_pack_temp1",
    "battery_pack_temp2", "battery_pack_temp3", "battery_pack_temp4",
    "battery_pack_temp5", "battery_pack_temp6", "bms_dcdcoutput_voltage",
    "bus_voltage", "cell_voltage_min", "cell_voltage_max", "charge_amhr_bms00",
    "charge_mode_mbms", "discharge_amphr_bms00", "measured_cell1_voltage",
    "measured_cell2_voltage", "measured_cell3_voltage", "measured_cell4_voltage",
    "measured_cell5_voltage", "measured_cell6_voltage", "measured_cell7_voltage",
    "measured_cell8_voltage", "measured_cell9_voltage", "measured_cell10_voltage",
    "measured_cell11_voltage", "measured_cell12_voltage", "measured_cell13_voltage",
    "measured_cell14_voltage", "balancing_started_due_deviation_count", "pack_voltage",
    "pdu_temp1", "pdu_temp_afe", "pack_soc", "pack_soh", "time_charge80_fc",
    "time_charge_optimum_fc", "time_charge80_sc", "time_charge_full_sc",
    "total_balancing_duration", "total_charge_time", "generic_bms_data",
    "balancing_temp", "overall_charge_voltage_limit", "display_soc",
    "bms00_cell_balancing_temperature", "charge_current_limit00",
    "discharge_current_limit00", "charge_voltage_limit00", "bms00_pdu_delta_temperature",
    "bms_vehicle_software_version", "overall_discharge_current_limit",
    "overall_charge_current_limit", "charging_voltage_available_range",
    "charging_current_availablerange", "charging_rate", "soc_val_from_min_ocv",
    "soc_debug1", "factored_discharge_amphr", "factored_charge_amphr",
    "serial_number", "evse_identification_low_byte", "evse_identification_high_byte",
    "discharge_mode", "ts_bms"
]

# Configure Loguru logger
logger.remove() # Remove default handler
logger.add(
    sys.stderr,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="INFO"
)
logger.add(
    "logs/db_operations.log",  # Log file path
    rotation="10 MB",        # Rotate log file when it reaches 10 MB
    retention="7 days",      # Keep logs for 7 days
    compression="zip",       # Compress rotated log files
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"            # Log DEBUG level and above to file
)

def get_db_connection():
    """
    Establishes a connection to the PostgreSQL database using credentials
    from environment variables.
    Returns:
        psycopg2.connection or None: A connection object if successful, None otherwise.
    """
    if not all([DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD]):
        logger.error("Database connection details are not fully configured in .env or environment variables.")
        return None
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        logger.info(f"Successfully connected to PostgreSQL database '{DB_NAME}' on {DB_HOST}:{DB_PORT}")
        return conn
    except psycopg2.OperationalError as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        logger.error(f"Connection parameters used: HOST={DB_HOST}, PORT={DB_PORT}, DBNAME={DB_NAME}, USER={DB_USER}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred during database connection: {e}")
        return None

if __name__ == '__main__':
    # Example usage:
    logger.info("Attempting to establish a database connection...")
    connection = get_db_connection()
    if connection:
        logger.info("Connection successful. Closing connection.")
        connection.close()
    else:
        logger.error("Failed to establish a database connection.")

    logger.debug("This is a debug message.")
    logger.info("This is an info message.")
    logger.warning("This is a warning message.")
    logger.error("This is an error message.")
    logger.critical("This is a critical message.")