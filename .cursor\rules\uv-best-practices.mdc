---
description: 
globs: 
alwaysApply: true
---

## UV Best Practices for Project Structuring

- Use UV virtual environments for isolation `uv venv`
- Prefer `uv add <package_1> <package_2>`
- Do not modify @pyproject.toml or add any requirements.txt, instead:
    - Use `uv run` for scripts within project directory and `uv run python -m <dir>.<module>` for sub directories.
    - Run `uv add <package_n>` command whenever using a few package

### Project Organization
- Use standard project structure with `src` directory and package

## Astral UV - Working on Projects

When working on Astral UV projects:

1. Follow established coding patterns in the codebase
2. Be consistent with existing style and organization
3. Write comprehensive tests for new functionality
4. Document all public APIs and modules
5. Update documentation when making significant changes
6. Run type checking and linting before submitting changes

## Error checking and linting with [ruff](mdc:https:/github.com/astral-sh/ruff)

- Run `uv run ruff check --fix <file_path>` and act on the received data  
- If ruff or pytest doesn't exist then `uv add --dev ruff` or `uv add --dev pytest`