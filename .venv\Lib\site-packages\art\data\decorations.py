# -*- coding: utf-8 -*-
"""Decorations data."""
angry1 = ["  ୧༼ಠ益ಠ༽︻╦╤─", ""]
arrow1 = ["➶➶➶➶➶", "➷➷➷➷➷"]
arrow2 = ["↤↤↤↤↤", "↦↦↦↦↦"]
arrow3 = ["↫↫↫↫↫", "↬↬↬↬↬"]
arrow4 = ["–--´¯`----»", "«----´¯`–--"]
arrow5 = ["‹—•°º¤°(", ")°¤º°•—›"]
arrow6 = ["⇇⇇⇇", "⇉⇉⇉"]
arrow7 = ["⇉⇉⇉", "⇇⇇⇇"]
arrow8 = ["]|I{•------» [", "] «------•}I|["]
arrow_wave1 = ["(¯`·.¸¸.·´¯`·.¸¸.-> [", "<-.¸¸.·´¯`·.¸¸.·´¯)"]
arrow_wave2 = ["°·.¸.·°¯°·.¸.·°¯°·.¸.-> [", "] <-.¸.·°¯°·.¸.·°¯°·.¸.·°"]
ball1 = ["●●--●●--●●", "●●--●●--●●"]
ball2 = ["··●(`●-", "-●´)●··"]
ball3 = ["●·(¯¨°¹●.", ".●¹°¨¯)·●"]
barcode1 = ["▌│█║▌║▌║ ", " ║▌║▌║█│▌"]
bazar1 = ["[ꔊꔊꔊ[🍉]", "[🍓]ꔊꔊꔊ]"]
block1 = ["■■■■■■■■□□□", "□□□■■■■■■■■"]
block2 = ["▄▀▀▄▀▀▄", "▄▀▀▄▀▀▄"]
bow1 = ["---»--@-}", "{-@--«---"]
bubble = ["ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ▫▫·∙∙∙∙∙", "∙∙∙∙∙·▫▫ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ"]
cat1 = ["｢(◔ω◔「)三", "三三三ʅ(；◔౪◔)ʃ"]
cat2 = ["☆(ﾉ^o^)ﾉ‥‥", "‥…━━━━〇(^~^)"]
cat3 = ["＼＼\\(۶•̀ᴗ•́)۶//／／", "\\\\٩(•́⌄•́๑)و////"]
cell1 = ["╔╣█╠╗╚", "╝╔╣█╠╗"]
champion1 = ["◄[🏆]► ", " ◄[🥇]►"]
chess1 = ["▀▄▀▄▀▄", "▄▀▄▀▄▀"]
confused1 = ["¯\\_(ツ)_/¯", "¯\\_(ツ)_/¯"]
confused2 = ["¨°o.O", "O.o°¨"]
cross1 = ["✖✖✖✖", "✖✖✖✖"]
depressed = ["‿︵‿︵(ಥ﹏ಥ)‿︵‿︵", ""]
diamon3 = ["¯`-●=»>◆<«=●", "●=»>◆<«=●-´¯"]
diamond1 = ["◄]·♦·»", "«·♦·[►"]
diamond2 = ["-·●·♦^v¯`♦)", "(♦¯`v^♦·●·-"]
egypt1 = ["╭₪₪₪", "₪₪₪╮"]
emotions1 = ["╱(●_●)(^_^)", "(^_^)(●_●)╲"]
fancy1 = ["╰⊱♥⊱╮ღ꧁", "꧂ღ╭⊱♥≺"]
fancy10 = ["჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ჻", "჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ჻"]
fancy11 = ["☆彡彡", "ミミ⛧"]
fancy12 = ["★彡彡", "ミミ★"]
fancy13 = ["»»——☠——«", "»——☠——««"]
fancy14 = [" ★━━─", "─━━★"]
fancy15 = ["■━■━■━■", "■━■━■━■"]
fancy16 = ["└╏ ･", "･ ╏┐"]
fancy17 = ["●～●～", "～●～●"]
fancy18 = ["((¯*.».( ¯*♥ »", "« ♥* ¯).«.*¯))"]
fancy19 = ["✧∭✧∰✧∭✧", "✧∭✧∰✧∭✧"]
fancy2 = ["★彡", "彡★"]
fancy20 = ["●▬▬▬๑۩۩๑▬▬▬●", "●▬▬▬๑۩۩๑▬▬▬●"]
fancy21 = ["(¯`·._.·", "·._.·´¯)"]
fancy22 = ["×÷·.·´¯`·)»", "«(·´¯`·.·÷×"]
fancy23 = ["· ··^v´¯`×)", "(×´¯`v^·· ·"]
fancy24 = [".·´¯`·->", "<-·´¯`·."]
fancy25 = ["- - --^[", "]^-- - -"]
fancy26 = ["(¯`·»o«·´¯`·>", "<·´¯`·»o«·´¯)"]
fancy27 = [").•ˆ•+*¨", "¨*+•ˆ•.("]
fancy28 = ["•·.·¯`·.·•", "•·.·¯`·.·•"]
fancy29 = ["•·.·´¯`·.·•", "•·.·´¯`·.·•"]
fancy3 = ["·٠•●♥ Ƹ̵̡Ӝ̵̨̄Ʒ ♥●•٠·˙", "˙·٠•●♥ Ƹ̵̡Ӝ̵̨̄Ʒ ♥●•٠·˙"]
fancy30 = ["(¯`·._)", "(¯`·._)"]
fancy31 = ["·°¯`·•", "•·´¯°·"]
fancy32 = [".ҳ̸Ҳ̸Ҳ̸ҳ•.•´¯`•", "•´¯`•.•..ҳ̸Ҳ̸Ҳ̸ҳ."]
fancy33 = ["×º°”˜`”°º×", "×º°”˜`”°º×"]
fancy34 = ["•]•·´º´·»", "«·´º´·•[•"]
fancy35 = ["-=₪۩۞۩₪=", "=₪۩۞۩₪=-"]
fancy36 = ["°•. °•. °•.", ".•° .•° .•°"]
fancy37 = ["(`·.·•", "•·.·')"]
fancy38 = ["+*¨^¨*+", "+*¨^¨*+"]
fancy39 = ["-·=»◆‡«=·-", "-·=»◆‡«=·-"]
fancy4 = ["٩(●̮̮̃•̃)=/̵͇̿̿/'̿̿ ̿̿", " ̿̿ ̿̿ ̿̿\\̵͇̿̿\\=(•̃●̮̮̃)۶"]
fancy40 = ["•°o.O", "O.o°•"]
fancy41 = ["©º°¨¨°º©", "©º°¨¨°º©"]
fancy42 = ["°·.¸.·°¯°·.¸.-<", ">-.¸.·°¯°·.¸.·°"]
fancy43 = ["•°l¯l_l¯l*", "*l¯l_l¯l•°"]
fancy44 = ["—(•·÷[", "]÷·•)—"]
fancy45 = ["·ï¡÷¡ï·", "·ï¡÷¡ï·"]
fancy46 = ["εїз", "εїз"]
fancy47 = ["•°•.•°•", "•°•.•°•"]
fancy48 = ["|¯|_|¯|_.°•. °•.->", "<-.•° .•°._|¯|_|¯|"]
fancy49 = ["¸.´)(`·[", "]·´)(` .¸"]
fancy5 = ["◇─◇──◇────◇", "◇─────◇──◇─◇"]
fancy50 = ["•]•·✦º✦·»", "«·✦º✦·•[•"]
fancy51 = [";`';_._;`';_.", "._;'`;_._.'`;"]
fancy52 = ["••.•´¯`•.••", "••.•´¯`•.••"]
fancy53 = ["|!¤*'~``~'*¤!||", "||!¤*'~``~'*¤!|"]
fancy54 = ["°•.•°o.O", "O.o°•.•°•"]
fancy55 = ["––––º•(-º", "º-)•º––––"]
fancy56 = ["ҳ̸Ҳ̸Ҳ̸ҳ(¯`·.•.", ".•.·´¯)ҳ̸Ҳ̸Ҳ̸ҳ"]
fancy57 = ["◄°l||l°", "°l||l°►"]
fancy58 = ["·∙·÷±‡±:∙", "∙:±‡±÷·∙·"]
fancy59 = ["(¯`v^÷••", "••÷^v´¯)"]
fancy6 = ["×º°”˜`”°º× [", "] ×º°”˜`”°º×"]
fancy60 = ["ø´¯`·¤»", "«¤·´¯`ø"]
fancy61 = ["º¯`·.·•", "•·.·´¯º"]
fancy62 = ["«(.·°¯`·->", "<-·´¯°·.)»"]
fancy63 = ["‹–…·´`·…–›", "‹–…·´`·…–›"]
fancy64 = [".·´`·.·´¯`·.›", "‹.·´¯`·.·´`·."]
fancy65 = ["(¯(_(¯`•", "•´¯)_)¯)•"]
fancy66 = ["[.·´¯`·.]", "[.·´¯`·.]"]
fancy67 = ["ì..·´`•", "•´`·..í"]
fancy68 = ["¯°•º¤◆¤º°¯", "¯°º¤◆¤º•°¯"]
fancy69 = ["<.•ˆ•…", "…•ˆ•.>"]
fancy7 = ["ڿڰۣ—☸ڿڰۣ—✨🌺✨", "✨🌺✨—ڿڰۣ—☸ڿڰۣ"]
fancy70 = ["‹‹¡Ì›–•¦[", "]¦•–‹Ì¡››"]
fancy71 = ["¯`·.__.·´¯»", "«¯`·.__.·´¯"]
fancy72 = ["‹v^v^v^•", "•^v^v^v›"]
fancy73 = ["{¯`·._.• .·´«", "»`·.•._.·´¯}"]
fancy74 = ["´¯`*¤.·´`·.¤", "¤.·´`·.¤*´¯`"]
fancy75 = ["‹—•((.•ˆ•…", "…•ˆ•.))•—›"]
fancy76 = ["÷(`•´`·.", ".·´`•´)÷"]
fancy77 = ["»-(¯`v´¯)-»", "«-(¯`v´¯)-«"]
fancy78 = ["¸„.-•~¹°”ˆ˜¨", "¨˜ˆ”°¹~•-.„¸"]
fancy79 = ["-漫~*'¨¯¨'*·舞~", "~舞*'¨¯¨'*·~漫-"]
fancy8 = ["·°¯`·•🌹■🌼■🌸ꕥ", "ꕥ🌸■🌼■🌹•·´¯°·"]
fancy80 = ["◦•●◉✿", "✿◉●•◦"]
fancy81 = ["ヾ(>u<●【*:..｡o○", "○o。..:*】●>u<)ﾉ"]
fancy82 = ["[]ðº°˜¨˜°ºð[]", "[]ðº°˜¨˜°ºð[]"]
fancy83 = ["●¸.•*¨Ƹ̵̡Ӝ̵̨̄Ʒ¨*•.¸●", "●¸.•*¨Ƹ̵̡Ӝ̵̨̄Ʒ¨*•.¸●"]
fancy84 = [".--ღஐƸ̵̡Ӝ̵̨̄Ʒஐღ--.", ".--ღஐƸ̵̡Ӝ̵̨̄Ʒஐღ--."]
fancy85 = ["⊱━━━.⋅εïз⋅.━━━⊰", "⊱━━━.⋅εïз⋅.━━━⊰"]
fancy86 = ["((*´_●｀☆ﾟ+.", ".+ﾟ☆´●_｀*))"]
fancy87 = ["꧁༺", "༻꧂"]
fancy88 = ["𒆜", "𒆜"]
fancy89 = ["ᕚ(", ")ᕘ"]
fancy9 = ["(◦′ᆺ‵◦) ♬° ✧❥✧¸.•*¨*✧♡✧", "✧♡✧*¨*•.❥"]
fancy90 = ["★彡(", ")彡★"]
fancy91 = ["◤✞", "✞◥"]
fancy92 = ["██▓▒­░⡷⠂", "⠐⢾░▒▓██"]
fancy93 = ["☆꧁✬◦°˚°◦. ", ".◦°˚°◦✬꧂☆"]
fancy94 = ["▞▞▞▞▞▖", "▝▞▞▞▞▞"]
fancy95 = ["𓂀", "𓂀"]
flame1 = ["ıllıllı", "ıllıllı"]
flower1 = ["꧁✿🌸╭⊱", "⊱╮🌸✿꧂"]
food1 = ["╭₪🍔₪🍟₪", "₪🍟₪🍔₪╮"]
food2 = ["︾︽🍍︾︽ ", "︽︾🍍︽︾"]
haha = ["⚡️¯\\_༼ ಥ ‿ ಥ ༽_/¯⚡", ""]
happy1 = ["o͡͡͡͡͡͡͡͡͡͡͡͡͡͡╮(｡❛ᴗ❛｡)╭o͡͡͡͡͡͡͡͡͡͡͡͡͡͡",
          "o͡͡͡͡͡͡͡͡͡͡͡͡͡͡╮(｡❛ᴗ❛｡)╭o͡͡͡͡͡͡͡͡͡͡͡͡͡͡"]
happy_new_year = ["❸ ❷ ❶ 🥂҉  ̡ ۫۰ Ձ٥۫١9 ۪ ҉   ۫۰̡´", "⭐️"]
hawaii1 = ["~-<🌴>-~", "~-<☀️>-~"]
hawaii2 = ["✈️─═∙∙∙▫▫ᵒᴼᵒ▫._", "▫▫..🌴"]
heart1 = ["~~<💚>~~", "~~<💚>~~"]
heart2 = ["~~><~~>💖", "💖<~~><~~"]
heart3 = ["─═ڿڿۣڿ═─💖─═ڿڿۣڿ═─", "─═ڿڿۣڿ═─💖─═ڿڿۣڿ═─"]
heart4 = ["༺♥༻❀༺♥༻💕﻿", "💕﻿༺♥༻❀༺♥༻"]
heart5 = ["💗💜.¸¸.•´¯`☆ºஇ•´♥", "♥•´இº☆.¸¸.•´¯`💜💗"]
heart6 = ["-♥-♡--^[", "]^--♡-♥-"]
heart7 = ["*•.¸♡", "♡¸.•*"]
heart8 = ["╚»♡«╝", "╚»♡«╝"]
heart9 = ["´*•.¸(*•.¸♥¸.•*´)¸.•*´", "´*•.¸(*•.¸♥¸.•*´)¸.•*´"]
heart10 = ["ミ💖", "💖彡"]
heart11 = ["෴❤️෴ ෴❤️෴", "෴❤️෴ ෴❤️෴"]
heart12 = ["💖´ *•.¸♥¸.•**", "**•.¸♥¸.•*´💖"]
heart13 = ["❤♡❤♡❤♡❤♡❤♡❤♡❤♡", "♡❤♡❤♡❤♡❤♡❤♡❤♡❤"]
heart14 = ["❤꧁ღ⊱♥", "♥⊱ღ꧂❤"]
heart15 = ["ミミ◦❧◦°˚°◦.¸¸◦°´❤*•.¸♥", "♥¸.•*❤´°◦¸¸.◦°˚°◦☙◦彡彡"]
line1 = ["┏━━━━━━┛", "┗━━━━━━┓"]
line2 = ["╭─━━━━━─╯", "╰─━━━━━─╮"]
line3 = ["┕━━━━ ⋆⋅☆⋅⋆ ━━┑", "┍━━ ⋆⋅☆⋅⋆ ━━━━┙"]
line4 = ["┕━━━━✿━━┑", "┍━━✿━━━━┙"]
love_music = ["¸.•*¨*•.¸♪¸.•*¨*•.¸♥¸.•*¨*•.¸", "¸.•*¨*•.¸♥¸.•*¨*•.¸♪¸.•*¨*•.¸"]
lucky1 = ["🌈ꔣᨐ ", " ᨐꔣ🌈"]
missile1 = ["💢🚀🚀💢", "💢🚀🚀💢"]
mountain1 = ["人人人", "人人人"]
mountain2 = ["╱╲╱╳╲╱╲", "╱╲╱╳╲╱╲"]
mountain3 = ["ᨏᨐᨓ", "ᨓᨐᨏ"]
music1 = ["🎧♪┏(°.°)┛🎼", "🎼┏(°.°)┛♪🎧"]
music2 = ["♪•]•·º♫·》»", "«《·♫º·•[•♪"]
music3 = ["·.¸¸.·♩♪♫", "♫♪♩·.¸¸.·"]
pencil1 = ["✎✐✎✐✎", "✐✎✐✎✐"]
poker1 = ["._|.<(+_+)>.|_. [", "] ._|.<(+_+)>.|_."]
puzzle1 = ["╚╝╔╗╚╝╔╗", "╚╝╔╗╚╝╔╗"]
puzzle2 = ["╭╮╰╯╭╮╰╯", "╭╮╰╯╭╮╰╯"]
puzzle3 = ["︹︺︹︺", "︺︹︺︹"]
sad1 = ["(-_-) ", " (-_-)"]
sad2 = ["ԅ[ •́ ﹏├┬┴┬┴", "ԅ[ •́ ﹏ •̀ ]و"]
sad3 = ["(^^(-_-)^^)", "(^^(-_-)^^)"]
sad4 = ["(-_-) [", "] (-_-)"]
smile1 = ["`•.,¸¸,.•´¯", "¯`•.,¸¸,.•´"]
snow1 = ["🌨❄•°•.•°•", "•°•.•°•❄🌨️"]
snow2 = ["✼　 ҉  ", "   ҉ 　✼"]
soccer1 = ["●●--●●◄⚽️► ", " ◄⚽️►●●--●●"]
star1 = ["【★】 ", " 【★】"]
star10 = ["-·=»★«=·-", "-·=»★«=·-"]
star11 = ["【★】", "【★】"]
star12 = ["★·.·¯`·.·★", "★·.·¯`·.·★"]
star13 = ["(★)(¯`·.●.●", "●.●.·¯)(★)"]
star14 = ["★★.·¯`★", "★¯`·.★★"]
star15 = ["╰☆☆", "☆☆╮"]
star16 = ["╚»★«╝", "╚»★«╝"]
star17 = ["*̥̻̥̻̥͙*̻̥̻̥͙*̥̻̥͙*̻̥͙*̥͙", "*̥̻̥̻̥͙*̻̥̻̥͙*̥̻̥͙*̻̥͙*̥͙"]
star18 = ["╰•★★", "★★•╯"]
star19 = ["٭⊹°⨳°·..·°⨳°", "°⨳°·..·°⨳°⊹٭"]
star2 = ["★·.·´¯`·.·★ ", " ★·.·´¯`·.·★"]
star20 = ["`✵•.¸,✵°✵.｡.✰", "✰.｡.✵°✵,¸.•✵´"]
star21 = ["·.★·.·´¯`·.·★", "★·.·´¯`·.·★.·"]
star22 = ["¨˜ˆ”°⍣~•✡⊹٭„¸", "¸„٭⊹✡•~⍣°”ˆ˜¨"]
star23 = ["°•.•°¤*✬.•°°•", "°•°•.✬*¤°•.•°"]
star24 = ["★¸.•☆•.¸★", "★⡀.•☆•.★"]
star25 = ["✬☆*.•⨳•.¤⊹٭", "٭⊹¤.•⨳•.*☆✬"]
star3 = ["╰☆⭐️", "⭐️☆╮"]
star4 = ["★・・・・・・★", "★・・・・・・★"]
star5 = ["【🌩】★【🌩】", "【🌩】★【🌩】"]
star6 = ["☆━━━━━━☆", "☆━━━━━━☆"]
star7 = ["・‥…━━━━━━━☆", "☆━━━━━━━…‥・"]
star8 = ["【☆】★【☆】", "【☆】★【☆】"]
star9 = ["☆❋──❁", "❃──❋"]
temple1 = ["エｴｪｪｪ", "ｪｪｪｴエ"]
title1 = ["»»————-", "————-««"]
tree1 = ["🎄✖🎄✖🎄⇉ ", "⇇🎄✖🎄✖🎄"]
wall1 = ["├┬┴┬┴", "┬┴┬┴┤"]
wave1 = ["▁ ▂ ▄ ▅ ▆ ▇ █", "█ ▇ ▆ ▅ ▄ ▂ ▁"]
wave2 = ["▉▇▆▅▄▃▂▂▂_", "_▂▂▃▄▅▆▇▉▉"]
wave3 = ["▂▃▅▇█▓▒░", "░▒▓█▇▅▃▂"]
wave4 = ["▂▃▄▅▆▇▉▉", "▉▉▇▆▅▄▃▂"]
wave5 = ["︵‿︵‿︵‿︵‿︵ ", "‿︵‿︵‿︵‿︵‿︵‿︵"]
wave6 = ["░▒▓█", "█▓▒░"]
wave7 = ["(¯`·._(¯`·._(¯`·._", "_.·¯)_.·¯)_.·¯)"]
wave8 = ["_/¯/__/¯/_", "_/¯/__/¯/_"]
wave9 = ["/)/)/)/)/)/)", "/)/)/)/)/)/)"]
