import os
import pandas as pd
from datetime import datetime
# import deration_dict from ../config/__init__.py
from utils import deration_dict

current_dir = os.getcwd()
input_folder = os.path.join(current_dir, 'input')
output_file = os.path.join(current_dir, 'output', 'pre_main.xlsx')

interested_columns = ['vehicle_odo', 'battery_pack_effective_temp', 
                      'overall_pack_soc', 'mode type', 'final_time', 'vehicle_speed_kmph', 
                      'battery_current', 'pack_voltage', 'mcu_temperature',
                      'bcm_vehicle_software_version', 'cell_voltage_min', 'cell_voltage_max',
                      'charge_current_limit00', 'discharge_current_limit00',
                      'pdu_temp1', 'pdu_temp_afe', 'motor_temperature', 'ts_bms']

def process_chunk(chunk):
    chunk = chunk.copy()  # Create a copy of the chunk DataFrame

    # odometer
    start_odo = chunk['vehicle_odo'].iloc[0]
    end_odo = chunk['vehicle_odo'].iloc[-1]
    
    # battery temp
    min_battery_temp = chunk['battery_pack_effective_temp'].min()
    max_battery_temp = chunk['battery_pack_effective_temp'].max()
    # for finding last_pdu1_temperature
    index5 = len(chunk) - 1
    while index5 >= 0:
        last_battery_temp = chunk['battery_pack_effective_temp'].iloc[index5]
        if not pd.isna(last_battery_temp):
            break  
        index5 -= 1
    

    # Filter out rows with non-null values in the 'overall_pack_soc' column
    non_null_chunk = chunk[chunk['overall_pack_soc'].notna()]

    # Check if there are non-null values before proceeding
    if non_null_chunk.empty:
        # Handle the case where there are no non-null values
        start_soc = None
        end_soc = None
    else:
        # Find the index of the first non-null value
        first_valid_index = non_null_chunk['overall_pack_soc'].first_valid_index()

        # Find the index of the last non-null value
        last_valid_index = non_null_chunk['overall_pack_soc'].last_valid_index()

        # Get the first and last available values
        start_soc = non_null_chunk['overall_pack_soc'][first_valid_index]
        end_soc = non_null_chunk['overall_pack_soc'][last_valid_index]

    # time
    start_time = chunk['final_time'].iloc[0]
    end_time = chunk['final_time'].iloc[-1]
    
    # battery current
    min_battery_current = chunk['battery_current'].min()
    max_battery_current = chunk['battery_current'].max()
    mode_battery_current = chunk['battery_current'].mode().iloc[0] if not chunk['battery_current'].mode().empty else None
    median_battery_current = chunk['battery_current'].median()
    
    # pdu_temp_1
    min_pdu1_temperature = chunk['pdu_temp1'].min()
    max_pdu1_temperature = chunk['pdu_temp1'].max()
    # for finding last_pdu1_temperature
    index1 = len(chunk) - 1
    while index1 >= 0:
        last_pdu1_temperature = chunk['pdu_temp1'].iloc[index1]
        if not pd.isna(last_pdu1_temperature):
            break  
        index1 -= 1

    # pdu_temp_afe
    min_pdu2_temperature = chunk['pdu_temp_afe'].min()
    max_pdu2_temperature = chunk['pdu_temp_afe'].max()
    # for finding last_pdu2_temperature
    index2 = len(chunk) - 1
    while index2 >= 0:
        last_pdu2_temperature = chunk['pdu_temp_afe'].iloc[index2]
        if not pd.isna(last_pdu2_temperature):
            break  
        index2 -= 1

    # motor_temperature
    min_motor_temperature = chunk['motor_temperature'].min()
    max_motor_temperature = chunk['motor_temperature'].max()
    # for finding last_motor_temperature
    index3 = len(chunk) - 1
    while index3 >= 0:
        last_motor_temperature = chunk['motor_temperature'].iloc[index3]
        if not pd.isna(last_motor_temperature):
            break  
        index3 -= 1
                   
    # charge current limit
    min_charge_current_limit = chunk['charge_current_limit00'].min()
    max_charge_current_limit = chunk['charge_current_limit00'].max()
    mode_charge_current_limit = chunk['charge_current_limit00'].mode().iloc[0] if not chunk['charge_current_limit00'].mode().empty else None
    median_charge_current_limit = chunk['charge_current_limit00'].median()
    
    # discharge current limit
    min_discharge_current_limit = chunk['discharge_current_limit00'].min()
    max_discharge_current_limit = chunk['discharge_current_limit00'].max()
    mode_discharge_current_limit = chunk['discharge_current_limit00'].mode().iloc[0] if not chunk['discharge_current_limit00'].mode().empty else None
    median_discharge_current_limit = chunk['discharge_current_limit00'].median()
    
    # mcu temp
    min_mcu_temperature = chunk['mcu_temperature'].min()
    max_mcu_temperature = chunk['mcu_temperature'].max()
    # for finding last_mcu_temperature
    index4 = len(chunk) - 1
    while index4 >= 0:
        last_mcu_temperature = chunk['mcu_temperature'].iloc[index4]
        if not pd.isna(last_mcu_temperature):
            break  
        index4 -= 1
 
    # vehicle software version
    vehicle_software_version = chunk['bcm_vehicle_software_version'].iloc[-1]
    
    # cell voltage
    cell_voltage_min = chunk['cell_voltage_min'].min()
    cell_voltage_max = chunk['cell_voltage_max'].max()
    
    # speed
    min_speed = chunk['vehicle_speed_kmph'].min()
    max_speed = chunk['vehicle_speed_kmph'].max()
    avg_speed = chunk[chunk['vehicle_speed_kmph'] != 0]['vehicle_speed_kmph'].mean() if not chunk[chunk['vehicle_speed_kmph'] != 0].empty else 0
    # calculated values
    odo_distance = end_odo - start_odo
    time_duration = end_time - start_time
    hours = time_duration.seconds // 3600
    minutes = (time_duration.seconds // 60) % 60
    duration = f"{hours} hrs and {minutes} mins"
    true_distance = avg_speed * (time_duration.total_seconds() / 3600)  # Convert seconds to hours

    # Assuming you have a pandas DataFrame named 'chunk'

    # Convert the 'ts_bms' column to a pandas datetime object
    chunk['ts_bms'] = pd.to_datetime(chunk['ts_bms'], format='%Y-%m-%dT%H:%M:%SZ', errors='coerce')

    # Sort the DataFrame by the 'ts_bms' column if it's not already sorted
    chunk = chunk.sort_values(by='ts_bms')

    # Calculate the time difference in hours and fill blanks with 0
    chunk['time_diff_hours'] = chunk['ts_bms'].diff().dt.total_seconds().fillna(0) / 3600  # Convert seconds to hours

    # energy consumption
    energy_consumption = (chunk['battery_current'] * chunk['pack_voltage'] * chunk['time_diff_hours']).sum()
    # Calculate energy consumption in Wh
    positive_energy = (chunk[chunk['battery_current'] > 0]['battery_current'] * chunk[chunk['battery_current'] > 0]['pack_voltage'] * chunk[chunk['battery_current'] > 0]['time_diff_hours']).sum()
    negative_energy = (chunk[chunk['battery_current'] < 0]['battery_current'] * chunk[chunk['battery_current'] < 0]['pack_voltage'] * chunk[chunk['battery_current'] < 0]['time_diff_hours']).sum()
    # make sure not in charge mode
    # Filter out rows where the 'vehicle mode' is not 'charge'
    chunk_filtered = chunk[~chunk['mode type'].str.contains('charge')]
    if not chunk_filtered.empty:
        # Define the number of parallel cells (14 in this case)
        num_parallel_cells = 14
        # Calculate the sum of regenerative Ampere-hours
        regen_ah = chunk_filtered['total_regen_amhr'].sum()
        # Calculate the difference in overall pack state of charge (SoC)
        diff_soc = chunk_filtered['overall_pack_soc'].iloc[-1] - chunk_filtered['overall_pack_soc'].iloc[0]
        # Calculate the total Ampere-hours consumed
        total_ah = diff_soc * 4.18 * num_parallel_cells
        # Calculate the percentage of regenerative energy relative to total energy
        perc_regen = (regen_ah / total_ah) * 100 if total_ah != 0 else 0
    else:
        regen_ah = None
        total_ah = None
        perc_regen = None
    # filter chuck for deration, where event_name is "HMI_Ctrl_Cmd"
    deration_chuck = chunk[chunk['event_name'] == "HMI_Ctrl_Cmd"]
    # filtered_list_from_deration_dict = [x for x in deration_chuck['event_value'].unique().tolist()]
    list_of_deration_signals = [x for x in deration_chuck['event_value'].unique().tolist() if x in deration_dict.keys()]
    deration_list_str = ','.join([str(x) for x in list_of_deration_signals])
    # breakpoint()
    return (
        start_odo, end_odo, min_battery_temp, max_battery_temp, last_battery_temp, start_soc, end_soc, start_time, end_time, 
        min_speed, max_speed, avg_speed, odo_distance, duration, true_distance, 
        energy_consumption, positive_energy, negative_energy, min_battery_current, 
        max_battery_current, mode_battery_current, median_battery_current,
        min_mcu_temperature, max_mcu_temperature, last_mcu_temperature, vehicle_software_version,
        cell_voltage_min, cell_voltage_max, min_charge_current_limit,max_charge_current_limit,mode_charge_current_limit,median_charge_current_limit,
        min_discharge_current_limit,max_discharge_current_limit,mode_discharge_current_limit,median_discharge_current_limit,
        min_pdu1_temperature, max_pdu1_temperature, last_pdu1_temperature, min_pdu2_temperature, max_pdu2_temperature, last_pdu2_temperature,
        min_motor_temperature, max_motor_temperature, last_motor_temperature, regen_ah, total_ah, perc_regen, deration_list_str
    )
result_data = []

for filename in os.listdir(input_folder):
    if filename.endswith('.csv'):
        file_path = os.path.join(input_folder, filename)
        dtype_options = { 'mode type': str, }
        df = pd.read_csv(file_path, dtype=dtype_options, low_memory=False )
        df['final_time'] = pd.to_datetime(df['final_time'])
        
        charger_change_indices = df[df['mode type'] != df['mode type'].shift()].index.tolist()
        
        for i in range(len(charger_change_indices)):
            if i == len(charger_change_indices) - 1:
                chunk = df.iloc[charger_change_indices[i]:]
            else:
                chunk = df.iloc[charger_change_indices[i]:charger_change_indices[i+1]]
            
            charger_type = chunk['mode type'].iloc[0]
            ([
              start_odo, end_odo, min_battery_temp, max_battery_temp, last_battery_temp, start_soc, end_soc, start_time, end_time,
              min_speed, max_speed, avg_speed, odo_distance, duration, true_distance, energy_consumption, positive_energy, negative_energy,
              min_battery_current, max_battery_current, mode_battery_current, median_battery_current,
              min_mcu_temperature, max_mcu_temperature, last_mcu_temperature, vehicle_software_version,
              cell_voltage_min, cell_voltage_max, min_charge_current_limit,max_charge_current_limit,mode_charge_current_limit,median_charge_current_limit,
              min_discharge_current_limit,max_discharge_current_limit,mode_discharge_current_limit,median_discharge_current_limit,
              min_pdu1_temperature, max_pdu1_temperature, last_pdu1_temperature, min_pdu2_temperature, max_pdu2_temperature, last_pdu2_temperature,
              min_motor_temperature, max_motor_temperature, last_motor_temperature, regen_ah, total_ah, perc_regen, deration_list_str
              ]) = process_chunk(chunk)
            
            start_time_formatted = start_time.strftime("%Y-%m-%d %H:%M:%S")
            end_time_formatted = end_time.strftime("%Y-%m-%d %H:%M:%S")

            result_data.append([
                filename, charger_type, start_soc, end_soc, start_odo, end_odo, odo_distance, true_distance, 
                min_battery_temp, max_battery_temp, last_battery_temp, start_time_formatted, end_time_formatted, duration, min_speed, max_speed, avg_speed, 
                energy_consumption, positive_energy, negative_energy, min_battery_current, max_battery_current, 
                mode_battery_current, median_battery_current, min_mcu_temperature, max_mcu_temperature, last_mcu_temperature,
                vehicle_software_version, cell_voltage_min, cell_voltage_max, min_charge_current_limit,max_charge_current_limit,mode_charge_current_limit,median_charge_current_limit,
                min_discharge_current_limit,max_discharge_current_limit,mode_discharge_current_limit,median_discharge_current_limit,
                min_pdu1_temperature, max_pdu1_temperature, last_pdu1_temperature, min_pdu2_temperature, max_pdu2_temperature, last_pdu2_temperature,
                min_motor_temperature, max_motor_temperature, last_motor_temperature, regen_ah, total_ah, perc_regen, deration_list_str
            ])
        print(f"Finished processing {filename}")
        
        if result_data:  # Check if result_data is not empty
            result_data.append([None] * len(result_data[0]))  # Add an empty row after processing each file
        
print("All files processed")

result_df = pd.DataFrame(result_data, columns=
            [
            'File Name', 'mode type', 'start soc', 'end soc','start odo', 'end odo','odo distance', 'true distance',
            'min battery temp', 'max battery temp', 'last battery temp',  'start time', 'end time', 'duration','min speed', 'max speed','avg_speed',
            'energy_consumption', 'positive_energy', 'negative_energy',
            'min_battery_current', 'max_battery_current', 'mode_battery_current','median_battery_current',
            'min_mcu_temperature', 'max_mcu_temperature', 'last_mcu_temperature', 'vehicle_software_version',
            'cell_voltage_min', 'cell_voltage_max', 'min_charge_current_limit','max_charge_current_limit','mode_charge_current_limit','median_charge_current_limit',
            'min_discharge_current_limit','max_discharge_current_limit','mode_discharge_current_limit','median_discharge_current_limit', 
            'min_pdu1_temperature', 'max_pdu1_temperature', 'last_pdu1_temperature', 'min_pdu2_temperature', 'max_pdu2_temperature', 'last_pdu2_temperature',
            'min_motor_temperature', 'max_motor_temperature', 'last_motor_temperature', 'regen_ah', 'total_ah', 'perc_regen', 'deration_list_str'
            ])

# Save the summary to a new Excel file
sheet_name = "pre_main"  # Set the desired sheet name
result_df.to_excel(output_file,sheet_name=sheet_name, index=False)