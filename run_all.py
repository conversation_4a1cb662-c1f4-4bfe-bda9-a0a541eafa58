import subprocess
import multiprocessing
import time, os
import subprocess
import multiprocessing
import time
from independent_uploader import push_daily

def run_subprocess(command):
    subprocess.run(command)

start_time = time.time()
# Get the virtual environment's activate script path
current_dir = os.getcwd()
python_path = os.path.join(current_dir,'.Env', 'Scripts', 'python.exe')
# run code/deration_seeker.py
# run_subprocess([python_path, 'code/add mode.py'])
if __name__ == '__main__':
    # List of commands to run sequentially
    sequential_commands = [
        [python_path, 'drive/download_csv.py'],
        [python_path, 'code/add mode.py'],
    ]
    # List of commands to run in parallel
    parallel_commands = [
        [python_path, 'code/main.py'],
        [python_path, 'code/faults.py'],
        # [python_path, 'code/dte_drop.py'],
    ]
    sequential_commands.append([python_path, 'code/pre_main.py']) if [python_path, 'code/main.py'] in parallel_commands else None
    sequential_commands.insert(0, [python_path, 'clear_all.py']) if [python_path, 'drive/download_csv.py'] in sequential_commands else None
    sequential_commands.insert(0, [python_path, 'code/clear_op_db.py'])

    # Run sequential commands
    for command in sequential_commands:
        run_subprocess(command)
    processes = []
    for command in parallel_commands:
        process = multiprocessing.Process(target=run_subprocess, args=(command,))
        processes.append(process)
        process.start()
    # Wait for all parallel processes to finish
    finish_flag = False
    for process in processes:
        process.join()
        finish_flag = True
    if finish_flag:
        # run deration.py file
        print("Running deration.py")
        run_subprocess([python_path, 'code/deration.py'])
        # run_subprocess([python_path, 'code/event_count.py'])
        run_subprocess([python_path, 'code/cloud_data_status.py'])
        # track the time taken to run the code in minutes
        # sleep for 5 seconds
        time.sleep(5)
        # run RUN_DB_UPLOAD.bat
        print("Running RUN_DB_UPLOAD.bat")
        run_subprocess([os.path.join(current_dir,'RUN_DB_UPLOAD.bat')])
        print(f"--- {round((time.time() - start_time)/60, 2)} minutes ---")
        time.sleep(10)
        push_daily(1)
        print("All Executions Completed Successfully")

