# code to clear <current_dir>/output/*.xlsx and <current_dir>/output/DB/*.csv files
import os
import glob

current_dir = os.getcwd()
output_dir = os.path.join(current_dir, 'output')
db_dir = os.path.join(output_dir, 'DB')
# terminate Excel
try:
    import win32com.client as win32
    # Try to use taskkill command, handle if not available
    result = os.system("taskkill /f /im EXCEL.EXE")
    if result != 0:
        print("Warning: taskkill command not available or Excel not running")
except Exception as e:
    print(f"Warning: Could not terminate Excel processes: {e}")
    pass

# delete all files in output_dir
if os.path.exists(output_dir):
    for file in glob.glob(os.path.join(output_dir, '*.xlsx')):
        try:
            os.remove(file)
            print(f"Deleted: {file}")
        except Exception as e:
            print(f"Warning: Could not delete {file}: {e}")
else:
    print(f"Warning: Output directory {output_dir} does not exist")

if os.path.exists(db_dir):
    for file in glob.glob(os.path.join(db_dir, '*.csv')):
        try:
            os.remove(file)
            print(f"Deleted: {file}")
        except Exception as e:
            print(f"Warning: Could not delete {file}: {e}")
else:
    print(f"Warning: DB directory {db_dir} does not exist")