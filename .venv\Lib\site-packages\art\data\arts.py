# -*- coding: utf-8 -*-
"""1-line art data."""
art_dic = {
    "fish1": "><(((('>",
    "fish2": "><>",
    "fish3": "`·.¸¸ ><((((º>.·´¯`·><((((º>    ",
    "fish4": "><>     ><>",
    "fish5": "<><",
    "fish6": "<`)))><",
    "house": "__̴ı̴̴̡̡̡ ̡͌l̡̡̡ ̡͌l̡*̡̡ ̴̡ı̴̴̡ ̡̡͡|̲̲̲͡͡͡ ̲▫̲͡ ̲̲̲͡͡π̲̲͡͡ ̲̲͡▫̲̲͡͡ ̲|̡̡̡ ̡ ̴̡ı̴̡̡ ̡͌l̡̡̡̡.___",
    "care crowd": "(-(-_(-_-)_-)-)",
    "monster": "٩(̾●̮̮̃̾•̃̾)۶",
    "monster2": "٩(- ̮̮̃-̃)۶",
    "boombox1": "♫♪.ılılıll|̲̅̅●̲̅̅|̲̅̅=̲̅̅|̲̅̅●̲̅̅|llılılı.♫♪",
    "butterfly": "Ƹ̵̡Ӝ̵̨̄Ʒ",
    "finger1": "╭∩╮(Ο_Ο)╭∩╮",
    "pistols1": "¯¯̿̿¯̿̿'̿̿̿̿̿̿̿'̿̿'̿̿̿̿̿'̿̿̿)͇̿̿)̿̿̿̿ '̿̿̿̿̿̿\\̵͇̿̿\\=(•̪̀●́)=o/̵͇̿̿/'̿̿ ̿ ̿̿",
    "finger2": "┌∩┐(◣_◢)┌∩┐",
    "finger3": "ಠ︵ಠ凸",
    "finger4": "┌∩┐(>_<)┌∩┐",
    "heart1": "»-(¯`·.·´¯)->",
    "mouse1": '----{,_,">',
    "mouse2": " .       ~~(__^·>",
    "mouse3": "<·^__)~~          .",
    "mouse4": '—-{,_,”><",_,}----',
    "mouse5": "<:3 )~~~~",
    "mouse6": "<^__)~",
    "mouse7": "~(__^>",
    "worm": "_/\\__/\\__0>",
    "koala": "@( * O * )@",
    "monkey": "@('_')@",
    "waves": "°º¤ø,¸¸,ø¤º°`°º¤ø,¸,ø¤°º¤ø,¸¸,ø¤º°`°º¤ø,¸",
    "glasses": "-@-@-",
    "rose1": "--------{---(@",
    "rose2": "@}}>-----",
    "rose3": "@-->--->---",
    "rose4": "@}~}~~~",
    "rose5": "@-}--",
    "rose6": "@)}---^-----",
    "rose7": "@->-->---",
    "star in my eyes": "<*_*>",
    "looking face": "ô¿ô",
    "sleeping": "(-.-)Zzz...",
    "sleeping baby": "[{-_-}] ZZZzz zz z...",
    "pistols2": "̿' ̿'\\̵͇̿̿\\з=(◕_◕)=ε/̵͇̿̿/'̿'̿ ̿",
    "pistols3": " ̿̿ ̿̿ ̿’̿̿’̿\\̵͇̿̿\\з=( ͡ °_̯͡° )=ε/̵͇̿̿/’̿̿’̿ ̿ ̿̿ ̿̿",
    "pistols4": "(•̪●)=ε/̵͇̿̿/’̿’̿ ̿ ̿̿ ̿ ̿””",
    "pistols5": "̿̿ ̿̿ ̿̿ ̿'̿'\\̵͇̿̿\\з=( ͠° ͟ʖ ͡°)=ε/̵͇̿̿/'̿̿ ̿ ̿ ̿ ̿ ̿",
    "knife1": ")xxxxx[;;;;;;;;;>",
    "knife2": ")xxx[::::::::::>",
    "coffee1": "c[_]",
    "coffee2": "l_D",
    "coffee3": "l_P",
    "coffee4": "l_B",
    "robot1": "d[ o_0 ]b",
    "pig1": "^(*(oo)*)^",
    "needle1": "┣▇▇▇═─",
    "needle2": "|==|iiii|>----- ",
    "cat1": "=^..^=",
    "fish swim": "¸.·´¯`·.´¯`·.¸¸.·´¯`·.¸><(((º>",
    "sword1": "(===||:::::::::::::::>",
    "rock on1": "\\,,/(^_^)\\,,/",
    "rock on2": "\\m/(-_-)\\m/",
    "caterpillar": ",/\\,/\\,/\\,/\\,/\\,/\\,o",
    "sword2": "▬▬ι═══════ﺤ    -═══════ι▬▬",
    "professor": '"""⌐(ಠ۾ಠ)¬"""',
    "sad1": "ε(´סּ︵סּ`)з",
    "sword3": "ס₪₪₪₪§|(Ξ≥≤≥≤≥≤ΞΞΞΞΞΞΞΞΞΞ>",
    "airplane1": " ‛¯¯٭٭¯¯(▫▫)¯¯٭٭¯¯’",
    "cassette1": "|[●▪▪●]|",
    "cassette2": "[¯ↂ■■ↂ¯]",
    "car race": "∙،°.  ˘Ô≈ôﺣ   » » »",
    "robot2": " c[○┬●]כ ",
    "happy": " ۜ\\(סּںסּَ` )/ۜ",
    "happy face": "ヽ(´▽`)/",
    "love in my eye1": "(♥_♥)",
    "love in my eye2": "(｡❤◡❤｡)",
    "love in my eye3": "(❤◡❤)",
    "cat2": "龴ↀ◡ↀ龴",
    "cat3": "^.--.^",
    "cat4": "(=^ェ^=)",
    "face": "•|龴◡龴|•",
    "big nose": "˚∆˚",
    "big eyes": "⺌∅‿∅⺌",
    "woman": "▓⚗_⚗▓",
    "stars": "✌⊂(✰‿✰)つ✌",
    "hairstyle": "⨌⨀_⨀⨌",
    "eyes": "℃ↂ_ↂↃ",
    "cat face": "⦿⽘⦿",
    "cute cat": "^⨀ᴥ⨀^",
    "cute face": "(｡◕‿◕｡)",
    "cute face2": "(ღ˘◡˘ღ)",
    "cute face3": "✿◕ ‿ ◕✿",
    "cute face4": "❀◕ ‿ ◕❀",
    "cute face5": "(✿◠‿◠)",
    "cute face6": "(◕‿◕✿)",
    "cute face7": "☾˙❀‿❀˙☽",
    "cute face8": "(◡‿◡✿)",
    "cute face9": "ლ(╹◡╹ლ)",
    "upset": "◤(¬‿¬)◥",
    "nose": "\\˚ㄥ˚\\",
    "pirate": "✌(◕‿-)✌",
    "happy2": "⎦˚◡˚⎣",
    "awesome": "<:3 )~~~",
    "dagger": "cxxx|;:;:;:;:;:;:;:;>",
    "umadbro": "¯\\_(ツ)_/¯ ",
    "table flip": "(╯°□°）╯︵ ┻━┻",
    "table flip2": "(ﾉಥ益ಥ）ﾉ﻿ ┻━┻",
    "table flip3": "┬─┬ノ( º _ ºノ)",
    "table flip4": "(ノಠ益ಠ)ノ彡┻━┻",
    "table flip5": "┬──┬﻿ ¯\\_(ツ)",
    "table flip6": "┻━┻ ︵﻿ ¯\\(ツ)/¯ ︵ ┻━┻",
    "table flip7": "(╯°□°)╯︵ ┻━┻ ︵ ╯(°□° ╯)",
    "table flip8": "(╯°Д°）╯︵ /(.□ . \\)",
    "table flip9": "(ノ^_^)ノ┻━┻ ┬─┬ ノ( ^_^ノ)",
    "table flip10": "(/ .□.)\\ ︵╰(゜Д゜)╯︵ /(.□. \\)",
    "linqan": ":Q___",
    "sad2": "(✖╭╮✖)",
    "sad3": "(◑﹏◐)",
    "sad4": "(◕_◕)",
    "sad5": "(´ᗣ｀)",
    "sad6": "Y_Y",
    "eye closed": " (╯_╰)",
    "inlove": "(✿ ♥‿♥) ",
    "cry": " (╯︵╰,)",
    "help": "٩(͡๏̯͡๏)۶",
    "birds": "~(‾▿‾)~",
    "pig2": "༼☉ɷ⊙༽",
    "bullshit": "|3ᵕᶦᶦᶳᶣᶨᶵ",
    "gun1": "︻╦╤─",
    "gun2": "︻デ═一",
    "gun3": "╦̵̵̿╤─ ҉ ~ •",
    "gun4": "︻╦̵̵͇̿̿̿̿╤──",
    "bird": " (⌒▽⌒)﻿",
    "sword4": " |O/////[{:;:;:;:;:;:;:;:;>",
    "elephant": "°j°m",
    "headphone1": "d[-_-]b",
    "headphone2": "d(-_-)b",
    "headphone3": "(W)",
    "up": "(◔/‿\\◔)",
    "airplane2": "✈",
    "airplane3": "✈ ✈ ———- ♒✈",
    "real face": "( ͡° ͜ʖ ͡°)﻿",
    "angry face": "(⋟﹏⋞)",
    "heart2": "♡♡",
    "boobs": "(.)(.)",
    "boobs2": "（·人·）",
    "sperm": "~~o",
    "ping pong": "( •_•)O*¯`·.¸.·´¯`°Q(•_• )",
    "chess": "♞▀▄▀▄♝▀▄ ",
    "scissors": "✄",
    "chair": "╦╣",
    "zombie": "'º_º'",
    "hybrix": "ʕʘ̅͜ʘ̅ʔ",
    "bear": "ʕ•ᴥ•ʔ",
    "bear2": "(ʳ ´º㉨º)",
    "bear GTFO": "ʕ •`ᴥ•´ʔ",
    "bear squiting": "ʕᵔᴥᵔʔ",
    "cheer": "  ^(¤o¤)^",
    "cheers": "（ ^_^）o自自o（^_^ ）",
    "hug me": "(っ◕‿◕)っ",
    "arrow1": "»»---------------------►",
    "arrow2": "XXX-------->",
    "rak": "/⦿L⦿\\",
    "nope": "t(-_-t)",
    "panda": "ヽ(￣(ｴ)￣)ﾉ",
    "guitar": "c====(=#O| ) ~~ ♬·¯·♩¸¸♪·¯·♫¸ ",
    "rocket": "∙∙∙∙∙·▫▫ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ☼)===>",
    "ghost": "‹’’›(Ͼ˳Ͽ)‹’’›",
    "hal": "@_'-'",
    "cthulhu": "^(;,;)^",
    "cthulhu2": "( ;,;)",
    "sat": "'(◣_◢)'",
    "what?": "ة_ة",
    "king": "-_-",
    "tron": "(\\/)(;,,;)(\\/)",
    "homer": "(_8(|)",
    "fox": "-^^,--,~",
    "singing": "d(^o^)b¸¸♬·¯·♩¸¸♪·¯·♫¸¸",
    "at what cost": "ლ(ಠ益ಠლ)",
    "atish": "(| - _ - |)",
    "zable": "ಠ_ರೃ",
    "trumpet": "-=iii=<()",
    "teddy": "ˁ(⦿ᴥ⦿)ˀ",
    "dice": "[: :]",
    "bee": "¸.·´¯`·¸¸.·´¯`·.¸.-<\\^}0=:",
    "ukulele": "{ o }==(::) ",
    "perky": "( ๏ Y ๏ )",
    "snail1": "'-'_@_",
    "snail2": "'\\Q___",
    "mango": ") _ _ __/°°¬",
    "decorate": "▂▃▅▇█▓▒░۩۞۩        ۩۞۩░▒▓█▇▅▃▂",
    "kirby": "(つ -‘ _ ‘- )つ",
    "bunny": "(\\_/) ",
    "kiss": "(o'3'o)",
    "kiss2": "(︶ε︶メ)",
    "kiss3": "╮(︶ε︶メ)╭",
    "sorreh bro": "(◢_◣)",
    "owlkin": "(ᾢȍˬȍ)ᾢ ļ ļ ļ ļ ļ",
    "yolo": "Yᵒᵘ Oᶰˡʸ Lᶤᵛᵉ Oᶰᶜᵉ",
    "formula1 car": "\\ō͡≡o˞̶",
    "dummy": "<-|-'_'-|->",
    "rope": "╚(▲_▲)╝",
    "rare": "┌ಠ_ಠ)┌∩┐ ᶠᶸᶜᵏ♥ᵧₒᵤ",
    "chess pieces": "♚ ♛ ♜ ♝ ♞ ♟ ♔ ♕ ♖ ♗ ♘ ♙",
    "sparkling heart": "-`ღ´-",
    "weather": "☼ ☀ ☁ ☂ ☃ ☄ ☾ ☽ ❄ ☇ ☈ ⊙ ☉ ℃ ℉ ° ❅ ✺ ϟ",
    "stars2": "⋆ ✢ ✣ ✤ ✥ ✦ ✧ ✩ ✪ ✫ ✬ ✭ ✮ ✯ ✰ ★",
    "upsidedown": "( ͜。 ͡ʖ ͜。)",
    "nathan": "♪└(￣◇￣)┐♪└(￣◇￣)┐♪└(￣◇￣)┐♪",
    "cat smile": "≧◔◡◔≦﻿",
    "old lady boobs": "|\\o/\\o/|",
    "glasses2": "ᒡ◯ᵔ◯ᒢ",
    "religious": "☪ ✡ † ☨ ✞ ✝ ☥ ☦ ☓ ♁ ☩",
    "sniperstars": "✯╾━╤デ╦︻✯",
    "kokain": " ̿ ̿' ̿'\\̵͇̿̿\\з=(•̪●)=ε/̵͇̿̿/'̿''̿ ̿",
    "bagel": "nln >_< nln",
    "crying1": "Ỏ̷͖͈̞̩͎̻̫̫̜͉̠̫͕̭̭̫̫̹̗̹͈̼̠̖͍͚̥͈̮̼͕̠̤̯̻̥̬̗̼̳̤̳̬̪̹͚̞̼̠͕̼̠̦͚̫͔̯̹͉͉̘͎͕̼̣̝͙̱̟̹̩̟̳̦̭͉̮̖̭̣̣̞̙̗̜̺̭̻̥͚͙̝̦̲̱͉͖͉̰̦͎̫̣̼͎͍̠̮͓̹̹͉̤̰̗̙͕͇͔̱͕̭͈̳̗̭͔̘̖̺̮̜̠͖̘͓̳͕̟̠̱̫̤͓͔̘̰̲͙͍͇̙͎̣̼̗̖͙̯͉̠̟͈͍͕̪͓̝̩̦̖̹̼̠̘̮͚̟͉̺̜͍͓̯̳̱̻͕̣̳͉̻̭̭̱͍̪̩̭̺͕̺̼̥̪͖̦̟͎̻̰_Ỏ̷͖͈̞̩͎̻̫̫̜͉̠̫͕̭̭̫̫̹̗̹͈̼̠̖͍͚̥͈̮̼͕̠̤̯̻̥̬̗̼̳̤̳̬̪̹͚̞̼̠͕̼̠̦͚̫͔̯̹͉͉̘͎͕̼̣̝͙̱̟̹̩̟̳̦̭͉̮̖̭̣̣̞̙̗̜̺̭̻̥͚͙̝̦̲̱͉͖͉̰̦͎̫̣̼͎͍̠̮͓̹̹͉̤̰̗̙͕͇͔̱͕̭͈̳̗̭͔̘̖̺̮̜̠͖̘͓̳͕̟̠̱̫̤͓͔̘̰̲͙͍͇̙͎̣̼̗̖͙̯͉̠̟͈͍͕̪͓̝̩̦̖̹̼̠̘̮͚̟͉̺̜͍͓̯̳̱̻͕̣̳͉̻̭̭̱͍̪̩̭̺͕̺̼̥̪͖̦̟͎̻̰ ",
    "angry2": "( ͠° ͟ʖ ͡°)﻿",
    "3": "ᕙ༼ ,,ԾܫԾ,, ༽ᕗ",
    "5": "ᕙ༼ ,,இܫஇ,, ༽ᕗ",
    "fuck you": "nlm (-_-) mln ",
    "head shot": "->~∑≥_≤)",
    "meow": "ฅ^•ﻌ•^ฅ",
    "metal": "\\m/_(>_<)_\\m/",
    "killer": "(⌐■_■)--︻╦╤─ - - - (╥﹏╥) ",
    "fu": "(ಠ_ಠ)┌∩┐",
    "ankush": "︻デ┳═ー*----*",
    "sex symbol": "◢♂◣◥♀◤◢♂◣◥♀◤",
    "barbell": "▐━━━━━▌",
    "sniper rifle": "︻デ┳═ー",
    "being draged": "╰(◣﹏◢)╯",
    "possessed": "<>_<>",
    "jokeranonimous": "╭∩╮ (òÓ,) ╭∩╮",
    "jokeranonimous2": "╭∩╮(ô¿ô)╭∩╮",
    "epic gun": "︻┳デ═— ",
    "love": "ⓛⓞⓥⓔ",
    "eric": ">--) ) ) )*>",
    "puls": "––•–√\\/––√\\/––•––",
    "sky free": "ѧѦ ѧ  ︵͡︵  ̢ ̱ ̧̱ι̵̱̊ι̶̨̱ ̶̱   ︵ Ѧѧ  ︵͡ ︵   ѧ Ѧ    ̵̗̊o̵̖  ︵  ѦѦ ѧ ",
    "smug bastard": "(‾⌣‾)",
    "tie-fighter": "|—O—|",
    "kyubey": "／人 ⌒ ‿‿ ⌒ 人＼",
    "kyubey2": "／人 ◕‿‿◕ 人＼",
    "love2": " ~♡ⓛⓞⓥⓔ♡~",
    "dancee": "♪┏(°.°)┛┗(°.°)┓┗(°.°)┛┏(°.°)┓ ♪",
    "mtmtika": ":o + :p = 69",
    "ak-47": "︻┳デ═—",
    "eaten apple": "[===]-'",
    "huhu": "█▬█ █▄█ █▬█ █▄█",
    "faydre": "(U) [^_^] (U) ",
    "domino": "[: :|:::]",
    "honeycute": "❤◦.¸¸.  ◦✿",
    "superman": "-^mOm^-",
    "nose2": "|'L'|",
    "hell yeah": "(òÓ,)_\\,,/",
    "roke": "_\\m/",
    "crayons": "((̲̅ ̲̅(̲̅C̲̅r̲̅a̲̅y̲̅o̲̅l̲̲̅̅a̲̅( ̲̅((>",
    "fish invasion": "›(̠̄:̠̄c  ›(̠̄:̠̄c  (¦Ҝ  (¦Ҝ  ҉  -  -  -   ¦̺͆¦  ▪▌",
    "bender": " ¦̵̱ ̵̱ ̵̱ ̵̱ ̵̱(̢ ̡͇̅└͇̅┘͇̅ (▤8כ−◦",
    "sunny day": "☁ ▅▒░☼‿☼░▒▅ ☁",
    "happy birthday 1": "ዞᏜ℘℘Ꮍ ℬℹℛʈዞᗬᏜᎽ",
    "line brack": "●▬▬▬▬๑۩۩๑▬▬▬▬▬●",
    "med": "ب_ب",
    "melp1": "(<>..<>)",
    "melp2": "(<(<>(<>.(<>..<>).<>)<>)>)",
    "happy square": "【ツ】",
    "snowman1": "☃",
    "i kill you": " ̿ ̿̿'̿̿\\̵͇̿̿\\=(•̪●)=/̵͇̿̿/'̿̿ ̿ ̿",
    "happy3": "㋡",
    "happy4": "^_^",
    "happy5": "[^_^]",
    "happy6": "(ツ)",
    "happy7": "【シ】",
    "happy8": "㋛",
    "happy9": "(シ)",
    "happy10": "(´ツ｀)",
    "happy11": "( ＾◡＾)っ",
    "happy12": "┏(＾0＾)┛┗(＾0＾)┓",
    "happy13": "(°⌣°)",
    "happy14": "٩(^‿^)۶",
    "happy15": "(•‿•)",
    "happy16": "ó‿ó",
    "happy17": "٩◔‿◔۶",
    "happy18": "ಠ◡ಠ",
    "happy19": "●‿●",
    "happy20": "( '‿' )",
    "happy21": "^‿^",
    "happy22": "┌( ಠ‿ಠ)┘",
    "happy23": "(˘◡˘)",
    "happy24": "☯‿☯",
    "happy25": "\\(• ◡ •)/",
    "happy26": "( ͡ʘ ͜ʖ ͡ʘ)",
    "happy27": "( ͡• ͜ʖ ͡• )",
    "jaymz": " (•̪●)==ε/̵͇̿​̿/’̿’̿ ̿ ̿̿    `(•.°)~",
    "long rose": "---------------------{{---<((@)",
    "kirby dance": "<(''<)  <( ' ' )>  (> '')>",
    "death star defense team": "|-o-| (-o-) |-o-|",
    "boobies": "(. )( .)",
    "dancing people": "‎(/.__.)/   \\(.__.\\)",
    "dance": '''(>'-')> <('_'<) ^('_')\\- \\m/(-_-)\\m/ <( '-')> \\_( .")> <(._.)-`''',
    "pictou": "|\\_______(#*#)_______/|",
    "polar bear": "ˁ˚ᴥ˚ˀ",
    "go away bear": "╭∩╮ʕ•ᴥ•ʔ╭∩╮",
    "charly": "+:)",
    "train": "/˳˳_˳˳\\!˳˳X˳˳!(˳˳_˳˳)[˳˳_˳˳]",
    "spot": "(  . Y .  )",
    "westbound fish": "< )))) ><    ",
    "telephone": "ε(๏_๏)з】",
    "9/11 truth": "✈__✈ █ █ ▄ ",
    "spear": ">>-;;;------;;-->",
    "srs face": "(ಠ_ಠ)",
    "this is areku": "d(^o^)b",
    "robot boy": "◖(◣☩◢)◗",
    "med man": "(⋗_⋖)",
    "angry": " ლ(ಠ益ಠ)ლ",
    "zoidberg": "(\\/)(Ö,,,,Ö)(\\/) ",
    "eastbound fish": "><((((>",
    "kilroy was here": '" Ü "',
    "gtalk fit": "(•̪̀●́)=ε/̵͇̿̿/'̿̿ ̿ ̿̿  N --------{---(@",
    "thanks": "\\(^-^)/",
    "tidy up": "┬─┬⃰͡ (ᵔᵕᵔ͜ )",
    "dalek": " ̵̄/͇̐\\ ",
    "sean the sheep": "<('--')>",
    "party time": "┏(-_-)┛┗(-_-﻿ )┓┗(-_-)┛┏(-_-)┓",
    "kablewee": " 	̿' ̿'\\̵͇̿̿\\з=( ͡ °_̯͡° )=ε/̵͇̿̿/'̿'̿ ̿ ",
    "i dont care": "╭∩╮（︶︿︶）╭∩╮",
    "innocent face": "ʘ‿ʘ",
    "slenderman": "ϟƖΣNd€RMαN",
    "john lennon": "((ºjº))",
    "peace yo!": "(‾⌣‾)♉",
    "punch": "O=('-'Q)",
    "put the table back": "┬─┬﻿ ノ( ゜-゜ノ)",
    "russian boobs": "[.][.]",
    "fuck off": "t(-.-t)",
    "man tears": "ಥ_ಥ",
    "robber": " -╤╗_(◙◙)_╔╤-   -  -  -  \\o/ \\o/ \\o/",
    "facepalm": "(>ლ)",
    "yo": "__o000o__(o)(o)__o000o__",
    "cigarette1": "(̅_̅_̅_̅(̅_̅_̅_̅_̅_̅_̅_̅_̅̅_̅()ڪے",
    "cigarette2": "(____((____________()~~~",
    "cigarette3": "()___)____________)",
    "oar": "===========(8888)",
    "sword5": "<%%%%|==========>",
    "sword6": "o()xxxx[{::::::::::::::::::::::::::::::::::>",
    "sword7": "o==[]::::::::::::::::>",
    "sword8": "▬▬ι═══════> ",
    "sword9": " <═══════ι▬▬",
    "sword10": "O==I======>",
    "car": "`o##o>",
    "hacksaw": "[|^^^^^^^",
    "canoe": ".,.,\\______/,..,.,",
    "spider1": "//O\\",
    "machinegun": ",==,--",
    "roadblock": "X+X+X+X+X",
    "marge simpson": "()()():|",
    "homer simpson": "=(:o)",
    "bat1": "^O^",
    "bat2": " ^v^ ",
    "superman logo": "/s\\",
    "dna sample": "~",
    "double flip": "┻━┻ ︵ヽ(`Д´)ﾉ︵﻿ ┻━┻",
    "mail box": "|M|/",
    "ufo1": ".-=-.",
    "ufo2": ".-=o=-.",
    "spider2": "/\\oo/\\",
    "spider3": "///\\oo/\\\\\\",
    "spider4": "/╲/\\╭ºoꍘoº╮/\\╱\\",
    "american money1": "[($)]",
    "american money2": "[̲̅$̲̅(̲̅1̲̅)̲̅$̲̅]",
    "american money3": "[̲̅$̲̅(̲̅5̲̅)̲̅$̲̅]",
    "american money4": "[̲̅$̲̅(̲̅ιοο̲̅)̲̅$̲̅]",
    "american money5": "[̲̅$̲̅(̲̅2οο̲̅)̲̅$̲̅]",
    "british money": "[£::]",
    "teepee": "/|\\",
    "heart3": "<3",
    "angel1": "^i^",
    "shark": "~~~~~~^~~~~~",
    "regular ass": "(_!_)",
    "fat ass": "(__!__)",
    "kiss my ass": "(_x_)",
    "devil": "]:->",
    "shrug": "¯\\_(ツ)_/¯",
    "flex": "ᕙ(⇀‸↼‶)ᕗ",
    "why": "ლ( `Д’ ლ)",
    "meditation": "‿( ́ ̵ _-`)‿",
    "pac man": "ᗧ···ᗣ···ᗣ··",
    "emo": "(///_ ;)",
    "shark attack": "~~~~~~\\o/~~~~~/\\~~~~~",
    "shocked1": "(∩╹□╹∩)",
    "monocle": "(╭ರ_•́)",
    "piggy": "(∩◕(oo)◕∩)",
    "camera": '[◉"]',
    "what??": "(Ͼ˳Ͽ)..!!!",
    "neo": "(⌐■_■)--︻╦╤─ - - -",
    "lenny": "( ͡° ͜ʖ ͡°)",
    "snowing": "✲´*。.❄¨¯`*✲。❄。*。",
    "dick": "8====D",
    "wizard": "(∩ ͡° ͜ʖ ͡°)⊃━☆ﾟ. *",
    "wat": "ಠ_ಠ",
    "chu": "(´ε｀ )",
    "butt": "(‿|‿)",
    "sophie": '<XX""XX>',
    "joy": "n_n",
    "bautista": "(╯°_°）╯︵ ━━━",
    "smooth": "(づ ￣ ³￣)づ ⓈⓂⓄⓄⓉⒽ ",
    "fuckall": " 	╭∩╮（︶︿︶）╭∩╮ ",
    "penis": "8===D",
    "wat-wat": "Σ(‘◉⌓◉’)",
    "fail": "o(╥﹏╥)o",
    "dead eyes": "¿ⓧ_ⓧﮌ",
    "satan": "↑_(ΦwΦ;)Ψ",
    "bomb": "!!(　’ ‘)ﾉﾉ⌒●~*",
    "sleepy coffee": "( -_-)旦~",
    "high five": "( ⌒o⌒)人(⌒-⌒ )v",
    "wtf dude?": "＼(◑д◐)＞∠(◑д◐)",
    "fungry": "Σ_(꒪ཀ꒪」∠)_",
    "derp": "ヘ（。□°）ヘ",
    "drowning": "人人人ヾ( ;×o×)〃 人人人",
    "almost cared": "╰╏ ◉ 〜 ◉ ╏╯",
    "yessir": "∠(･`_´･ )",
    "coffee now": "{zzz}°°°( -_-)>c[_]",
    "round cat": "~(^._.)",
    "squee": "ヾ(◎o◎,,；)ﾉ",
    "round bird": ",(u°)>",
    "hoxom": "h(o x o )m",
    "squid": "くコ:彡",
    "man spider": "/╲/\\༼ *ಠ 益 ಠ* ༽/\\╱\\",
    "spell cast": "╰( ⁰ ਊ ⁰ )━☆ﾟ.*･｡ﾟ",
    "jazz musician": "ヽ(⌐■_■)ノ♪♬",
    "myancat": "mmmyyyyy<⦿⽘⦿>aaaannn",
    "gimme": "༼ つ ◕_◕ ༽つ",
    "crab": "(\\|) ._. (|/)",
    "playing in snow": "(╯^□^)╯︵ ❄☃❄ ",
    "sunglasses1": "(•_•)>⌐■-■ (⌐■_■)",
    "mini penis": "=D",
    "victory": "V(-.o)V",
    "dgaf": "┌∩┐(◣ _ ◢)┌∩┐",
    "ryans dick": "8======D",
    "eds dick": "8=D",
    "point": "(☞ﾟヮﾟ)☞",
    "afraid": "(　ﾟ Дﾟ)",
    "laughing": "(＾▽＾)",
    "energy": " つ ◕_◕ ༽つ  つ ◕_◕ ༽つ ",
    "drunkenness": "ヽ（´ー｀）┌",
    "error": "(╯°□°)╯︵ ɹoɹɹƎ",
    "penis2": "○○)=======o)",
    "exchange": "(╯°□°）╯︵ ǝƃuɐɥɔxǝ",
    "excited": "☜(⌒▽⌒)☞",
    "singing2": "♪└(￣◇￣)┐♪",
    "acid": "⊂(◉‿◉)つ",
    "arrowhead": "⤜(ⱺ ʖ̯ⱺ)⤏",
    "awkward": "•͡˘㇁•͡˘",
    "because": "∵",
    "blackeye": "0__#",
    "catlenny": "( ͡° ᴥ ͡°)",
    "dab": "ヽ( •_)ᕗ",
    "damnyou": "(ᕗ ͠° ਊ ͠° )ᕗ",
    "depressed": "(︶︹︶)",
    "dunno": "¯\\(°_o)/¯",
    "dunno2": "\\_(シ)_/",
    "dunno3": "└㋡┘",
    "dunno4": "╘㋡╛",
    "dunno5": "٩㋡۶",
    "eeriemob": "(-(-_-(-_(-_(-_-)_-)-_-)_-)_-)-)",
    "envelope": "✉",
    "fart": "(ˆ⺫ˆ๑)<3",
    "feel perky": "(`･ω･´)",
    "finn": "| (• ◡•)|",
    "frown": "(ღ˘⌣˘ღ)",
    "glitter": "(*・‿・)ノ⌒*:･ﾟ✧",
    "gotit": "(☞ﾟ∀ﾟ)☞",
    "hello": "(ʘ‿ʘ)╯",
    "hello2": "（ツ）ノ",
    "loading1": "█▒▒▒▒▒▒▒▒▒",
    "loading2": "███▒▒▒▒▒▒▒",
    "loading3": "█████▒▒▒▒▒",
    "loading4": "███████▒▒▒",
    "loading5": "█████████▒",
    "loading6": "██████████",
    "meep": "\\(°^°)/",
    "cup1": "(▓",
    "cup2": "\\̅_̅/̷̚ʾ",
    "barcode1": "█║▌│ █│║▌ ║││█║▌ │║║█║ │║║█║",
    "barcode2": "║█║▌║█║▌│║▌║▌█║",
    "band aid": "(̲̅:̲̅:̲̅:̲̅[̲̅ ̲̅]̲̅:̲̅:̲̅:̲̅ )",
    "electrocardiogram1": "√v^√v^√v^√v^√♥",
    "electrocardiogram2": "v^v^v^v^√\\/♥",
    "electrocardiogram3": "/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\v^♥",
    "electrocardiogram4": "√v^√v^♥√v^√v^√",
    "smile": ":-)",
    "toungue out1": ":-Þ",
    "toungue out2": ":-P",
    "bad hair1": "=:-)",
    "bad hair2": "=:-(",
    "woops": ":-*",
    "screaming": ":-@",
    "full mouth": ":-I",
    "cussing": ":-#",
    "ready to cry": ":-}",
    "running": "ε=ε=ε=┌(;*´Д`)ﾉ",
    "crying2": ":~(",
    "druling1": ":-...",
    "druling2": ":-P~~~",
    "big kiss": ":-X",
    "french kiss": ":-XÞ",
    "wink": ";-)",
    "big smile": ":-D",
    "smirk": ":-,",
    "confused1": ":-/",
    "confused2": ":-\\",
    "confused3": "(°~°)",
    "confused4": "^^'",
    "confused5": "é_è",
    "confused6": "(˚ㄥ_˚)",
    "confused7": "(; ͡°_ʖ ͡°)",
    "confused8": "(´•_•`)",
    "confused9": "(ˇ_ˇ’!l)",
    "confused10": "(*'__'*)",
    "pursing lips": ':-"',
    "shocked2": ":-O",
    "really sad": ":-C",
    "baseball fan": "q:o)",
    "basking in glory": "ヽ(´ー｀)ノ",
    "angel2": "O:-)",
    "angry face2": "(╬ ಠ益ಠ)",
    "really mad": ">:-I",
    "licking lips": ":-9",
    "buck teeth": ":-B",
    "surprised1": "=:-o",
    "surprised2": "（　ﾟДﾟ）",
    "surprised3": "(O_o)",
    "surprised4": "(º_•)",
    "surprised5": "(º.º)",
    "surprised6": "⊙▃⊙",
    "surprised7": "O.o",
    "surprised8": "●_●",
    "surprised9": "(⊙̃.o)",
    "surprised10": "(⊙.◎)",
    "surprised11": "๏_๏",
    "surprised12": "(˚-˚)",
    "surprised13": "˚o˚",
    "surprised14": "(O.O)",
    "surprised15": "( ﾟoﾟ)",
    "surprised16": "◉_◉",
    "surprised17": "【•】_【•】",
    "surprised18": "(•ิ_•)",
    "surprised19": "⊙⊙",
    "surprised20": "͡๏_͡๏",
    "alien": "::)",
    "sunglasses2": "B-)",
    "don king": "==8-)",
    "devilish smile": ">:)",
    "devilish grin": ">:-D",
    "clowning": "*:o)",
    "bugs bunny": "E=B",
    "kitty": "=^. .^=",
    "kitty emote": "ᵒᴥᵒ#",
    "fido": "V•ᴥ•V",
    "fish skeleton1": ">-}-}-}->",
    "fish skeleton2": ">++('>",
    "fisticuffs": "ლ(｀ー´ლ)",
    "snowman2": '{ }( : ^ )( """" )( )',
    "marshmallows": "-()_)--()_)---",
    "nerd": "::(",
    "boombox2": "♫♪ |̲̅̅●̲̅̅|̲̅̅=̲̅̅|̲̅̅●̲̅̅| ♫♪",
    "equalizer": "▇ ▅ █ ▅ ▇ ▂ ▃ ▁ ▁ ▅ ▃ ▅ ▅ ▄ ▅ ▇",
    "playing cards": "[♥]]] [♦]]] [♣]]] [♠]]]",
    "playing cards waterfall": "🂱🂲🂳🂴🂵🂶🂷🂸🂹🂺🂻🂼🂽🂾🃁🃂🃃🃄🃅🃆🃇🃈🃉🃊🃋🃌🃍🃎"
                               "🃑🃒🃓🃔🃕🃖🃗🃘🃙🃚🃛🃜🃝🃞🂡🂢🂣🂤🂥🂦🂧🂨🂩🂪🂫🂬🂭🂮🂠🃏🃟",
    "playing cards waterfall (trump)": "🃠🃡🃢🃣🃤🃥🃦🃧🃨🃩🃪🃫🃬🃭🃮🃯🃰🃱🃲🃳🃴🃵",
    "playing cards hearts": "[♥]]]",
    "playing cards hearts waterfall": "🂠🂱🂲🂳🂴🂵🂶🂷🂸🂹🂺🂻🂼🂽🂾",
    "playing cards diamonds": "[♦]]]",
    "playing cards diamonds waterfall": "🃟🃁🃂🃃🃄🃅🃆🃇🃈🃉🃊🃋🃌🃍🃎",
    "playing cards clubs": "[♣]]]",
    "playing cards clubs waterfall": "🃏🃑🃒🃓🃔🃕🃖🃗🃘🃙🃚🃛🃜🃝🃞",
    "playing cards spades": "[♠]]]",
    "playing cards spades waterfall": "🃠🂡🂢🂣🂤🂥🂦🂧🂨🂩🂪🂫🂬🂭🂮",
    "spade bold": "♠",
    "heart bold": "♥",
    "diamond bold": "♦",
    "club bold": "♣",
    "spade regular": "♤",
    "heart regular": "♡",
    "diamond regular": "♢",
    "club regular": "♧",
    "joker1": "🂠",
    "joker2": "🃟",
    "joker3": "🃏",
    "joker4": "🃠",
    "loch ness monster": "_mmmP",
    "sheep": "Â°lÂ°(,,,,);",
    "pie fight": "---=======[}",
    "concerned": "(@_@)",
    "carpet roll": "@__",
    "infinity": "(X)",
    "computer mouse": "[E}",
    "volcano1": '/"\\',
    "volcano2": "/W\\",
    "volcano3": "/V\\",
    "squigle with spirals": "6\\9",
    "palm tree": "'T`",
    "crotch shot": "\\*/",
    "vagina": "(:)",
    "stealth fighter": "-^-",
    "tent1": "//\\",
    "tent2": "/\\\\",
    "power lines": "TTT",
    "tree stump": 'J"l',
    "hammer": "#==",
    "fork": "---=",
    "pipe": "====\\_/",
    "dead guy": "'==xx\\0",
    "dead girl": "'==>x\\9",
    "dead child": "'-=,o",
    "dude glasses1": "@[O],[O]",
    "dude glasses2": "@(o),(o)",
    "devious smile": "ಠ‿ಠ",
    "breakdown": "ಥ﹏ಥ",
    "disagree": "٩◔̯◔۶",
    "do you even lift bro?": "ᕦ(ò_óˇ)ᕤ",
    "tripping out": "q(❂‿❂)p",
    "discombobulated": "⊙﹏⊙",
    "sad and confused": "¯\\_(⊙︿⊙)_/¯",
    "japanese lion face": "°‿‿°",
    "confused scratch": "(⊙.☉)7",
    "worried": "(´･_･`)",
    "dear god why": "щ（ﾟДﾟщ）",
    "staring": "٩(๏_๏)۶",
    "pretty eyes": "ఠ_ఠ",
    "strut": "ᕕ( ᐛ )ᕗ",
    "zoned": "(⊙_◎)",
    "crazy": "ミ●﹏☉ミ",
    "trolling": "༼∵༽ ༼⍨༽ ༼⍢༽ ༼⍤༽",
    "angry troll": "ヽ༼ ಠ益ಠ ༽ﾉ",
    "sad face": "(ಥ⌣ಥ)",
    "hugger": "(づ￣ ³￣)づ",
    "stranger danger": "(づ｡◕‿‿◕｡)づ",
    "flip friend": "(ノಠ ∩ಠ)ノ彡( \\o°o)\\",
    "cry face": "｡ﾟ( ﾟஇ‸இﾟ)ﾟ｡",
    "cry troll": "༼ ༎ຶ ෴ ༎ຶ༽",
    "tgif": "“ヽ(´▽｀)ノ”",
    "dancing": "┌(ㆆ㉨ㆆ)ʃ",
    "sleepy": "눈_눈",
    "angry birds": "( ఠൠఠ )ﾉ",
    "no support": "乁( ◔ ౪◔)「      ┑(￣Д ￣)┍",
    "shy": "(๑•́ ₃ •̀๑)",
    "fly away": "⁽⁽ଘ( ˊᵕˋ )ଓ⁾⁾",
    "careless": "◔_◔",
    "love3": "♥‿♥",
    "love4": "(Ɔ ˘⌣˘)♥(˘⌣˘ C)",
    "touchy feely": "ԅ(≖‿≖ԅ)",
    "kissing": "( ˘ ³˘)♥ ",
    "kissing2": "( ˘з˘)ε˘`)",
    "kissing3": "(~˘з˘)~~(˘ε˘~)",
    "kissing4": "(っ˘з(O.O )♥",
    "kissing5": "(`˘з(•˘⌣˘•)",
    "kissing6": "(っ˘з(˘⌣˘ )",
    "shark face": "( ˇ෴ˇ )",
    "emo dance": "ヾ(-_- )ゞ",
    "dance2": "♪♪ ヽ(ˇ∀ˇ )ゞ",
    "opera": "ヾ(´〇`)ﾉ♪♪♪",
    "winnie the pooh": "ʕ •́؈•̀)",
    "boxing": "ლ(•́•́ლ)",
    "fight": "(ง'̀-'́)ง",
    "listening to headphones": "◖ᵔᴥᵔ◗ ♪ ♫ ",
    "robot3": "{•̃_•̃}",
    "seal": "(ᵔᴥᵔ)",
    "dislike1": "(Ծ‸ Ծ)",
    "winning": "(•̀ᴗ•́)و ̑̑",
    "zombie2": "[¬º-°]¬",
    "chasing": "''⌐(ಠ۾ಠ)¬''",
    "whisling": "(っ•́｡•́)♪♬",
    "injured": "(҂◡_◡)",
    "creeper": "ƪ(ړײ)‎ƪ​​",
    "eye roll": "⥀.⥀",
    "flying": "ح˚௰˚づ",
    "things that can_t be unseen": "♨_♨",
    "looking down": "(._.)",
    "im a hugger": "(⊃｡•́‿•̀｡)⊃",
    "wizard2": "(∩｀-´)⊃━☆ﾟ.*･｡ﾟ",
    "yun": "(っ˘ڡ˘ς)",
    "judging": "( ఠ ͟ʖ ఠ)",
    "tired": "( ͡ಠ ʖ̯ ͡ಠ)",
    "dislike2": "( ಠ ʖ̯ ಠ)",
    "hitchhicking": "(งツ)ว",
    "satisfied": "(◠﹏◠)",
    "sad and crying": "(ᵟຶ︵ ᵟຶ)",
    "stunna shades": "(っ▀¯▀)つ",
    "chicken": "ʚ(•｀",
    "barf": "(´ж｀ς)",
    "fuck you2": "(° ͜ʖ͡°)╭∩╮",
    "exorcism": "ح(•̀ж•́)ง †",
    "taking a dump": "(⩾﹏⩽)",
    "wave dance": "~(^-^)~",
    "happy hug": "\\(ᵔᵕᵔ)/",
    "resting my eyes": "ᴖ̮ ̮ᴖ",
    "peepers": "ಠಠ",
    "judgemental": "\\{ಠʖಠ\\}",
    "mad": "òÓ",
    "mad2": "(ノ`Д ́)ノ",
    "mad3": ">_<",
    "mad4": "~_~",
    "mad5": "Ծ_Ծ",
    "mad6": "⋋_⋌",
    "mad7": "(ノ≥∇≤)ノ",
    "mad8": "{{{(>_<)}}}",
    "mad9": "ƪ(`▿▿▿▿´ƪ)",
    "mad10": "(•ˋ _ ˊ•)",
    "swim": "＿（ッ）>＿/／",
    "swim2": "ー（ッ）」",
    "swim3": "＿（ッ）へ",
    "yawning": "＼(＾o＾)／",
    "bored": "╭∩╮<-L->╭∩╮"}
