# -*- coding: utf-8 -*-
'''
>>> import os
>>> import random
>>> from art import *
>>> font_list(mode="non-ascii")
abby :
ТΞꚂТ
<BLANKLINE>
akila :
ŦЄ$Ŧ
<BLANKLINE>
alissa :
ꞎᥱടꞎ
<BLANKLINE>
antrophobia :
тєѕт
<BLANKLINE>
ascii_roman :
τεsτ
<BLANKLINE>
atlantic :
ϯεςϯ
<BLANKLINE>
awa :
тєѕт
<BLANKLINE>
awcute :
téśt
<BLANKLINE>
awesome :
тεƨт
<BLANKLINE>
ayda :
Ť∈ᏕŤ
<BLANKLINE>
batman :
էεรէ
<BLANKLINE>
bianca :
եɛsե
<BLANKLINE>
black_bubble :
🅣🅔🅢🅣
<BLANKLINE>
black_square :
🆃🅴🆂🆃
<BLANKLINE>
bold_fraktur :
𝖙𝖊𝖘𝖙
<BLANKLINE>
bold_script :
𝓽𝓮𝓼𝓽
<BLANKLINE>
boom :
✞ɛֆ✞
<BLANKLINE>
bud1 :
tོ<PERSON><PERSON><PERSON><PERSON>tོ
<BLANKLINE>
bud2 :
tཽeཽsཽtཽ
<BLANKLINE>
callian :
ŧɇsŧ
<BLANKLINE>
carrier1 :
TᕮST
<BLANKLINE>
carrier2 :
TᙓST
<BLANKLINE>
celtic :
էεʂէ
<BLANKLINE>
chinese_mafia :
丁乇丂丁
<BLANKLINE>
cjk :
イモらイ
<BLANKLINE>
clay :
Ł୧ꚂŁ
<BLANKLINE>
contouring1 :
ⓣⓔⓢⓣ
<BLANKLINE>
contouring2 :
⒯⒠⒮⒯
<BLANKLINE>
contouring3 :
[̲̅t][̲̅e][̲̅s][̲̅t]
<BLANKLINE>
contouring4 :
(̲̅t)(̲̅e)(̲̅s)(̲̅t)
<BLANKLINE>
cool :
Ꮏ𐒢ᎴᎿ
<BLANKLINE>
coptic1 :
ⲧⲉ⳽ⲧ
<BLANKLINE>
coptic2 :
ⲧⲉ⳽ⲧ
<BLANKLINE>
cranky :
†εš†
<BLANKLINE>
crazy1 :
ɬєʂɬ
<BLANKLINE>
crazy2 :
☂€ⓢ☂
<BLANKLINE>
crazy3 :
Ƭ៩នƬ
<BLANKLINE>
cross_jesus :
†ҽŞ†
<BLANKLINE>
cruse :
ᖶᘿSᖶ
<BLANKLINE>
curly :
էҽʂէ
<BLANKLINE>
currency :
₮Ɇ₴₮
<BLANKLINE>
cute1 :
ƭєƨƭ
<BLANKLINE>
cute2 :
ƭҽʂƭ
<BLANKLINE>
cute3 :
եҽsե
<BLANKLINE>
dark_tattoo :
𝔱𝔢𝔰𝔱
<BLANKLINE>
dirty :
ẗệṩẗ
<BLANKLINE>
dirty2 :
ẗệṩẗ
<BLANKLINE>
dotted :
ẗëṡẗ
<BLANKLINE>
drako :
ϮꂅᏕϮ
<BLANKLINE>
drippy :
ȶᵉˢȶ
<BLANKLINE>
dwarf :
ᵗᵉˢᵗ
<BLANKLINE>
emoji :
🌴🎗💲🌴
<BLANKLINE>
fancy1 :
тεsт
<BLANKLINE>
fancy10 :
ɬɛʂɬ
<BLANKLINE>
fancy100 :
꓄ꏂꇙ꓄
<BLANKLINE>
fancy101 :
tᥱ⳽t
<BLANKLINE>
fancy102 :
ΓESΓ
<BLANKLINE>
fancy103 :
tẹṣt
<BLANKLINE>
fancy104 :
ƮᙓᔑƮ
<BLANKLINE>
fancy105 :
тešт
<BLANKLINE>
fancy106 :
ʈɛsʈ
<BLANKLINE>
fancy107 :
τεsτ
<BLANKLINE>
fancy108 :
ƚЄ$ƚ
<BLANKLINE>
fancy109 :
ᴛᴇꜱᴛ
<BLANKLINE>
fancy11 :
ՇєรՇ
<BLANKLINE>
fancy110 :
ₜₑₛₜ
<BLANKLINE>
fancy111 :
ᵗᵉˢᵗ
<BLANKLINE>
fancy112 :
тєѕт
<BLANKLINE>
fancy113 :
էҽʂէ
<BLANKLINE>
fancy114 :
≋t≋e≋s≋t
<BLANKLINE>
fancy115 :
░t░e░s░t
<BLANKLINE>
fancy116 :
Ⲅ𐒢ᎴⲄ
<BLANKLINE>
fancy117 :
ᏖᏋ𐒡Ꮦ
<BLANKLINE>
fancy118 :
ᏖᏋᏕᏖ
<BLANKLINE>
fancy119 :
ᎿᏋᎴᎿ
<BLANKLINE>
fancy12 :
tєѕt
<BLANKLINE>
fancy120 :
Ŧ€ŞŦ
<BLANKLINE>
fancy121 :
₮€$₮
<BLANKLINE>
fancy122 :
tєรt
<BLANKLINE>
fancy123 :
੮૯ς੮
<BLANKLINE>
fancy124 :
ŤẸŜŤ
<BLANKLINE>
fancy125 :
ţέşţ
<BLANKLINE>
fancy126 :
ƭɛֆƭ
<BLANKLINE>
fancy127 :
꓄ꍟꌗ꓄
<BLANKLINE>
fancy128 :
ԵȝՏԵ
<BLANKLINE>
fancy129 :
ｲ乇丂ｲ
<BLANKLINE>
fancy13 :
tєรt
<BLANKLINE>
fancy130 :
𐨠𐌴Ⲋ𐨠
<BLANKLINE>
fancy131 :
ᵗᵉˢᵗ
<BLANKLINE>
fancy132 :
𝕥𝕖𝕤𝕥
<BLANKLINE>
fancy133 :
𝙩𝙚𝙨𝙩
<BLANKLINE>
fancy134 :
𝒕𝒆𝒔𝒕
<BLANKLINE>
fancy135 :
𝐭𝐞𝐬𝐭
<BLANKLINE>
fancy136 :
𝘁𝗲𝘀𝘁
<BLANKLINE>
fancy137 :
𝖙𝖊𝖘𝖙
<BLANKLINE>
fancy138 :
𝓉ℯ𝓈𝓉
<BLANKLINE>
fancy139 :
𝓽𝓮𝓼𝓽
<BLANKLINE>
fancy14 :
ȶɛֆȶ
<BLANKLINE>
fancy140 :
ⲧⲉ𝛓ⲧ
<BLANKLINE>
fancy141 :
ｔｅｓｔ
<BLANKLINE>
fancy142 :
🅃🄴🅂🅃
<BLANKLINE>
fancy143 :
🆃🅴🆂🆃
<BLANKLINE>
fancy144 :
🅣🅔🅢🅣
<BLANKLINE>
fancy145 :
ⓣⓔⓢⓣ
<BLANKLINE>
fancy146 :
セ🝗丂セ
<BLANKLINE>
fancy147 :
ƚҽʂƚ
<BLANKLINE>
fancy148 :
𝚝ₑ𝘴𝚝
<BLANKLINE>
fancy15 :
✞ƎƧ✞
<BLANKLINE>
fancy16 :
ΓΞSΓ
<BLANKLINE>
fancy17 :
ᏆɛֆᏆ
<BLANKLINE>
fancy18 :
ԵҽՏԵ
<BLANKLINE>
fancy19 :
꓄ꍟꌗ꓄
<BLANKLINE>
fancy2 :
ㄒ乇丂ㄒ
<BLANKLINE>
fancy20 :
ᵀᴱˢᵀ
<BLANKLINE>
fancy21 :
тeѕт
<BLANKLINE>
fancy22 :
ŤƐらŤ
<BLANKLINE>
fancy23 :
ƬЄƧƬ
<BLANKLINE>
fancy24 :
ϮꂅᏕϮ
<BLANKLINE>
fancy25 :
ｲ乇丂ｲ
<BLANKLINE>
fancy26 :
†εš†
<BLANKLINE>
fancy27 :
tēŞt
<BLANKLINE>
fancy28 :
ƬΣƧƬ
<BLANKLINE>
fancy29 :
†ê§†
<BLANKLINE>
fancy3 :
ŤĔŚŤ
<BLANKLINE>
fancy30 :
ᖶᘿSᖶ
<BLANKLINE>
fancy31 :
ནპჰན
<BLANKLINE>
fancy32 :
꓄ꏂꑄ꓄
<BLANKLINE>
fancy33 :
꓅ꍟꌚ꓅
<BLANKLINE>
fancy34 :
ꋖꈼꌚꋖ
<BLANKLINE>
fancy35 :
੮૯ς੮
<BLANKLINE>
fancy36 :
ԵȝՏԵ
<BLANKLINE>
fancy37 :
ꋖꏹꌚꋖ
<BLANKLINE>
fancy38 :
ꋖꑀꈜꋖ
<BLANKLINE>
fancy39 :
ፕቿነፕ
<BLANKLINE>
fancy4 :
ᏆᎬsᏆ
<BLANKLINE>
fancy40 :
꓅ꑾꇘ꓅
<BLANKLINE>
fancy41 :
ț£§ț
<BLANKLINE>
fancy42 :
ţ€$ţ
<BLANKLINE>
fancy43 :
ᖶᙍSᖶ
<BLANKLINE>
fancy44 :
тεƨт
<BLANKLINE>
fancy45 :
тešт
<BLANKLINE>
fancy46 :
էεʂէ
<BLANKLINE>
fancy47 :
†ε$†
<BLANKLINE>
fancy48 :
էεรէ
<BLANKLINE>
fancy49 :
τεЅτ
<BLANKLINE>
fancy5 :
ᏖᏋᏕᏖ
<BLANKLINE>
fancy50 :
ҭἔṩҭ
<BLANKLINE>
fancy52 :
τEŠτ
<BLANKLINE>
fancy53 :
ƮęsƮ
<BLANKLINE>
fancy54 :
⊥ÈS⊥
<BLANKLINE>
fancy55 :
t€$t
<BLANKLINE>
fancy56 :
𝐭𝐞𝐬𝐭
<BLANKLINE>
fancy57 :
𝚝𝚎𝚜𝚝
<BLANKLINE>
fancy58 :
𝖙𝖊𝖘𝖙
<BLANKLINE>
fancy59 :
𝕥𝕖𝕤𝕥
<BLANKLINE>
fancy6 :
ƭεรƭ
<BLANKLINE>
fancy60 :
𝘁𝗲𝘀𝘁
<BLANKLINE>
fancy61 :
𝑡𝑒𝑠𝑡
<BLANKLINE>
fancy62 :
𝘵𝘦𝘴𝘵
<BLANKLINE>
fancy63 :
𝒕𝒆𝒔𝒕
<BLANKLINE>
fancy64 :
𝙩𝙚𝙨𝙩
<BLANKLINE>
fancy65 :
𝓉𝑒𝓈𝓉
<BLANKLINE>
fancy66 :
𝓽𝒆𝓼𝓽
<BLANKLINE>
fancy67 :
ｔｅｓｔ
<BLANKLINE>
fancy68 :
𝔱𝔢𝔰𝔱
<BLANKLINE>
fancy69 :
ᵗᵉᶳᵗ
<BLANKLINE>
fancy7 :
丅ᗴᔕ丅
<BLANKLINE>
fancy70 :
тeѕт
<BLANKLINE>
fancy71 :
тєѕт
<BLANKLINE>
fancy72 :
тєѕт
<BLANKLINE>
fancy73 :
ŧêšŧ
<BLANKLINE>
fancy74 :
ŧεşŧ
<BLANKLINE>
fancy75 :
ｲ乇ㄎｲ
<BLANKLINE>
fancy76 :
էҽʂէ
<BLANKLINE>
fancy77 :
ƚҽʂƚ
<BLANKLINE>
fancy78 :
tєรt
<BLANKLINE>
fancy79 :
†εš†
<BLANKLINE>
fancy8 :
tєรt
<BLANKLINE>
fancy80 :
ƭєƨƭ
<BLANKLINE>
fancy81 :
ƚЄ$ƚ
<BLANKLINE>
fancy82 :
Ŧ£ŞŦ
<BLANKLINE>
fancy83 :
τεȘτ
<BLANKLINE>
fancy84 :
tΞst
<BLANKLINE>
fancy85 :
ťĕʂť
<BLANKLINE>
fancy86 :
ᴛᴇsᴛ
<BLANKLINE>
fancy87 :
𝓽𝓮𝓼𝓽
<BLANKLINE>
fancy88 :
tᥱst
<BLANKLINE>
fancy89 :
𝗍𝖾𝗌𝗍
<BLANKLINE>
fancy9 :
тeѕт
<BLANKLINE>
fancy90 :
𝑡𝑒𝑠𝑡
<BLANKLINE>
fancy91 :
𝒕𝒆𝒔𝒕
<BLANKLINE>
fancy92 :
𝙩𝙚𝙨𝙩
<BLANKLINE>
fancy93 :
𝘵𝘦𝘴𝘵
<BLANKLINE>
fancy94 :
𝓉𝑒𝓈𝓉
<BLANKLINE>
fancy95 :
𝔱𝔢𝔰𝔱
<BLANKLINE>
fancy96 :
tᥱ⳽t
<BLANKLINE>
fancy97 :
тεѕт
<BLANKLINE>
fancy98 :
τεsτ
<BLANKLINE>
fancy99 :
тeѕт
<BLANKLINE>
fantasy2 :
ᡶꫀకᡶ
<BLANKLINE>
fari :
『t』『e』『s』『t』
<BLANKLINE>
fasion :
ŧεşŧ
<BLANKLINE>
flaky :
丅ᗴᔕ丅
<BLANKLINE>
flip :
ϝԍƨϝ
<BLANKLINE>
foreign_friends :
ṭєṡṭ
<BLANKLINE>
foxy :
ｔｅｓｔ
<BLANKLINE>
fraktur2 :
𝔱𝔢𝔰𝔱
<BLANKLINE>
full_width :
ｔｅｓｔ
<BLANKLINE>
funky_fresh :
тḙṧт
<BLANKLINE>
got :
ＴＥＳＴ
<BLANKLINE>
greek_legends :
TΣST
<BLANKLINE>
handwriting1 :
𝓉𝑒𝓈𝓉
<BLANKLINE>
handwriting2 :
ƚҽʂƚ
<BLANKLINE>
handwriting3 :
𝓉𝑒𝓈𝓉
<BLANKLINE>
hazy :
ŤƐらŤ
<BLANKLINE>
hideki :
丁乇丂丁
<BLANKLINE>
high_above :
ᵗᵉˢᵗ
<BLANKLINE>
hippie :
⊥ε﹩⊥
<BLANKLINE>
hisoka :
ｲ乇らｲ
<BLANKLINE>
hyves :
t€$t
<BLANKLINE>
instagram :
🅣🅔🅢🅣
<BLANKLINE>
kesia :
TΣST
<BLANKLINE>
knight :
ṮḕṠṮ
<BLANKLINE>
knight2 :
ṮḕṠṮ
<BLANKLINE>
laurine :
ŤƐらŤ
<BLANKLINE>
lilia :
test
<BLANKLINE>
livia :
եᥱsե
<BLANKLINE>
lolie :
тєѕт
<BLANKLINE>
lopioo :
ȶɛֆȶ
<BLANKLINE>
lord_of_the_ring :
тєѕт
<BLANKLINE>
love1 :
ƬƐSƬ
<BLANKLINE>
love2 :
тєsт
<BLANKLINE>
lucifer :
ᏆᎬsᏆ
<BLANKLINE>
malayalam :
੮૯ട੮
<BLANKLINE>
manga :
ㄒ乇丂ㄒ
<BLANKLINE>
messletters :
ᴛєѕᴛ
<BLANKLINE>
milka :
ƭҽʂƭ
<BLANKLINE>
minion :
ᴛᴇsᴛ
<BLANKLINE>
mirror :
ɈƨǝɈ
<BLANKLINE>
mirror_flip :
ʇsǝʇ
<BLANKLINE>
monospace :
𝚝𝚎𝚜𝚝
<BLANKLINE>
native_lands :
ƬꍟꌗƬ
<BLANKLINE>
neva :
੮૯ട੮
<BLANKLINE>
ninja :
ⱦēꞩⱦ
<BLANKLINE>
old_italic :
𐌕𐌄𐌔𐌕
<BLANKLINE>
orinda :
ꞎ𝚎𐒖ꞎ
<BLANKLINE>
ozana :
𝙩𝒆𝙨𝙩
<BLANKLINE>
paranormal :
tєst
<BLANKLINE>
parenthesized :
⒯⒠⒮⒯
<BLANKLINE>
pin1 :
t྇e྇s྇t྇
<BLANKLINE>
pin2 :
t༙྇e༙྇s༙྇t༙྇
<BLANKLINE>
rusify :
тё$т
<BLANKLINE>
russian :
TΞST
<BLANKLINE>
russian2 :
ƬƎƧƬ
<BLANKLINE>
samya :
ꞎ୧𐒖ꞎ
<BLANKLINE>
sarah :
ᏆℰЅᏆ
<BLANKLINE>
scammer :
těst
<BLANKLINE>
shanna :
ṬЄꕷṬ
<BLANKLINE>
shasha :
†€∫†
<BLANKLINE>
sheqi :
【t】【e】【s】【t】
<BLANKLINE>
sign :
t⃠e⃠s⃠t⃠
<BLANKLINE>
slammer :
ƮęsƮ
<BLANKLINE>
small_fancy :
ₜₑₛₜ
<BLANKLINE>
smallcaps2 :
ᴛᴇsᴛ
<BLANKLINE>
smallcaps3 :
ᴛᴇsᴛ
<BLANKLINE>
smila :
𝑡୧𐍃𝑡
<BLANKLINE>
smooth1 :
тєѕт
<BLANKLINE>
smooth2 :
тєѕт
<BLANKLINE>
smooth3 :
ƚҽʂƚ
<BLANKLINE>
special :
TEᔕT
<BLANKLINE>
squiggle1 :
ԵҽsԵ
<BLANKLINE>
squiggle2 :
ԵeՏԵ
<BLANKLINE>
strange :
τεȘτ
<BLANKLINE>
strikethrough :
ŧɇsŧ
<BLANKLINE>
stylish :
ȶєᏕȶ
<BLANKLINE>
subscript1 :
ₜₑₛₜ
<BLANKLINE>
subscript2 :
𝑡ₑ𝑠𝑡
<BLANKLINE>
sunday_cuddle :
тєѕт
<BLANKLINE>
sunny :
тḙṧт
<BLANKLINE>
superscript :
ᵗᵉˢᵗ
<BLANKLINE>
swirly :
੮૯ς੮
<BLANKLINE>
symbols :
☂€ⓢ☂
<BLANKLINE>
tai_viet :
ꪻꫀᦓꪻ
<BLANKLINE>
tarty1 :
<BLANKLINE>
████████╗███████╗░██████╗████████╗
╚══██╔══╝██╔════╝██╔════╝╚══██╔══╝
░░░██║░░░█████╗░░╚█████╗░░░░██║░░░
░░░██║░░░██╔══╝░░░╚═══██╗░░░██║░░░
░░░██║░░░███████╗██████╔╝░░░██║░░░
░░░╚═╝░░░╚══════╝╚═════╝░░░░╚═╝░░░
<BLANKLINE>
tarty2 :
<BLANKLINE>
▀█▀ █▀▀ █▀ ▀█▀
░█░ ██▄ ▄█ ░█░
<BLANKLINE>
<BLANKLINE>
tarty3 :
<BLANKLINE>
▀▀█▀▀ █▀▀ █▀▀ ▀▀█▀▀
──█── █▀▀ ▀▀█ ──█──
──▀── ▀▀▀ ▀▀▀ ──▀──
<BLANKLINE>
tarty4 :
<BLANKLINE>
▀▀█▀▀ █▀▀ █▀▀ ▀▀█▀▀
░░█░░ █▀▀ ▀▀█ ░░█░░
░░▀░░ ▀▀▀ ▀▀▀ ░░▀░░
<BLANKLINE>
tarty5 :
<BLANKLINE>
▜▛ █☰ ▟▛ ▜▛ 
<BLANKLINE>
tarty6 :
<BLANKLINE>
█─▄─▄─██▄─▄▄─██─▄▄▄▄██─▄─▄─█
███─█████─▄█▀██▄▄▄▄─████─███
▀▀▄▄▄▀▀▀▄▄▄▄▄▀▀▄▄▄▄▄▀▀▀▄▄▄▀▀
<BLANKLINE>
tarty7 :
<BLANKLINE>
████████████████████████████
█─▄─▄─██▄─▄▄─██─▄▄▄▄██─▄─▄─█
███─█████─▄█▀██▄▄▄▄─████─███
▀▀▄▄▄▀▀▀▄▄▄▄▄▀▀▄▄▄▄▄▀▀▀▄▄▄▀▀
<BLANKLINE>
tarty8 :
<BLANKLINE>
────────────────────────────────────────────────────────────────
─██████████████──██████████████──██████████████──██████████████─
─██░░░░░░░░░░██──██░░░░░░░░░░██──██░░░░░░░░░░██──██░░░░░░░░░░██─
─██████░░██████──██░░██████████──██░░██████████──██████░░██████─
─────██░░██──────██░░██──────────██░░██──────────────██░░██─────
─────██░░██──────██░░██████████──██░░██████████──────██░░██─────
─────██░░██──────██░░░░░░░░░░██──██░░░░░░░░░░██──────██░░██─────
─────██░░██──────██░░██████████──██████████░░██──────██░░██─────
─────██░░██──────██░░██──────────────────██░░██──────██░░██─────
─────██░░██──────██░░██████████──██████████░░██──────██░░██─────
─────██░░██──────██░░░░░░░░░░██──██░░░░░░░░░░██──────██░░██─────
─────██████──────██████████████──██████████████──────██████─────
────────────────────────────────────────────────────────────────
<BLANKLINE>
tarty9 :
<BLANKLINE>
████████████████████████████████████████████████████████████████
█░░░░░░░░░░░░░░██░░░░░░░░░░░░░░██░░░░░░░░░░░░░░██░░░░░░░░░░░░░░█
█░░▄▀▄▀▄▀▄▀▄▀░░██░░▄▀▄▀▄▀▄▀▄▀░░██░░▄▀▄▀▄▀▄▀▄▀░░██░░▄▀▄▀▄▀▄▀▄▀░░█
█░░░░░░▄▀░░░░░░██░░▄▀░░░░░░░░░░██░░▄▀░░░░░░░░░░██░░░░░░▄▀░░░░░░█
█████░░▄▀░░██████░░▄▀░░██████████░░▄▀░░██████████████░░▄▀░░█████
█████░░▄▀░░██████░░▄▀░░░░░░░░░░██░░▄▀░░░░░░░░░░██████░░▄▀░░█████
█████░░▄▀░░██████░░▄▀▄▀▄▀▄▀▄▀░░██░░▄▀▄▀▄▀▄▀▄▀░░██████░░▄▀░░█████
█████░░▄▀░░██████░░▄▀░░░░░░░░░░██░░░░░░░░░░▄▀░░██████░░▄▀░░█████
█████░░▄▀░░██████░░▄▀░░██████████████████░░▄▀░░██████░░▄▀░░█████
█████░░▄▀░░██████░░▄▀░░░░░░░░░░██░░░░░░░░░░▄▀░░██████░░▄▀░░█████
█████░░▄▀░░██████░░▄▀▄▀▄▀▄▀▄▀░░██░░▄▀▄▀▄▀▄▀▄▀░░██████░░▄▀░░█████
█████░░░░░░██████░░░░░░░░░░░░░░██░░░░░░░░░░░░░░██████░░░░░░█████
████████████████████████████████████████████████████████████████
<BLANKLINE>
thin2 :
ｔｅｓｔ
<BLANKLINE>
thin3 :
ｔｅｓｔ
<BLANKLINE>
tiny :
ᴛᴇᴤᴛ
<BLANKLINE>
tiny2 :
ᴛᴇꜱᴛ
<BLANKLINE>
tiny_caps :
ᴛᴇᴤᴛ
<BLANKLINE>
upside_down1 :
ʇǝsʇ
<BLANKLINE>
upside_down2 :
ʇǝsʇ
<BLANKLINE>
vaporwave :
ｔｅｓｔ
<BLANKLINE>
veronika :
TᏋꚂT
<BLANKLINE>
vip :
ƬΣƧƬ
<BLANKLINE>
white_bubble :
ⓣⓔⓢⓣ
<BLANKLINE>
white_square :
🅃🄴🅂🅃
<BLANKLINE>
wiggly :
⊥ÈS⊥
<BLANKLINE>
zakia :
Ꚍ୧ઽꚌ
<BLANKLINE>
>>> art_list(mode="non-ascii")
3
ᕙ༼ ,,ԾܫԾ,, ༽ᕗ
******************************
5
ᕙ༼ ,,இܫஇ,, ༽ᕗ
******************************
9/11 truth
✈__✈ █ █ ▄
******************************
acid
⊂(◉‿◉)つ
******************************
afraid
(　ﾟ Дﾟ)
******************************
airplane1
 ‛¯¯٭٭¯¯(▫▫)¯¯٭٭¯¯’
******************************
airplane2
✈
******************************
airplane3
✈ ✈ ———- ♒✈
******************************
ak-47
︻┳デ═—
******************************
almost cared
╰╏ ◉ 〜 ◉ ╏╯
******************************
american money2
[̲̅$̲̅(̲̅1̲̅)̲̅$̲̅]
******************************
american money3
[̲̅$̲̅(̲̅5̲̅)̲̅$̲̅]
******************************
american money4
[̲̅$̲̅(̲̅ιοο̲̅)̲̅$̲̅]
******************************
american money5
[̲̅$̲̅(̲̅2οο̲̅)̲̅$̲̅]
******************************
angry
 ლ(ಠ益ಠ)ლ
******************************
angry birds
( ఠൠఠ )ﾉ
******************************
angry face
(⋟﹏⋞)
******************************
angry face2
(╬ ಠ益ಠ)
******************************
angry troll
ヽ༼ ಠ益ಠ ༽ﾉ
******************************
angry2
( ͠° ͟ʖ ͡°)﻿
******************************
ankush
︻デ┳═ー*----*
******************************
arrow1
»»---------------------►
******************************
arrowhead
⤜(ⱺ ʖ̯ⱺ)⤏
******************************
at what cost
ლ(ಠ益ಠლ)
******************************
awkward
•͡˘㇁•͡˘
******************************
band aid
(̲̅:̲̅:̲̅:̲̅[̲̅ ̲̅]̲̅:̲̅:̲̅:̲̅ )
******************************
barbell
▐━━━━━▌
******************************
barcode1
█║▌│ █│║▌ ║││█║▌ │║║█║ │║║█║
******************************
barcode2
║█║▌║█║▌│║▌║▌█║
******************************
barf
(´ж｀ς)
******************************
basking in glory
ヽ(´ー｀)ノ
******************************
bautista
(╯°_°）╯︵ ━━━
******************************
bear
ʕ•ᴥ•ʔ
******************************
bear GTFO
ʕ •`ᴥ•´ʔ
******************************
bear squiting
ʕᵔᴥᵔʔ
******************************
bear2
(ʳ ´º㉨º)
******************************
because
∵
******************************
bee
¸.·´¯`·¸¸.·´¯`·.¸.-<\^}0=:
******************************
being draged
╰(◣﹏◢)╯
******************************
bender
 ¦̵̱ ̵̱ ̵̱ ̵̱ ̵̱(̢ ̡͇̅└͇̅┘͇̅ (▤8כ−◦
******************************
big eyes
⺌∅‿∅⺌
******************************
big nose
˚∆˚
******************************
bird
 (⌒▽⌒)﻿
******************************
birds
~(‾▿‾)~
******************************
bomb
!!(　’ ‘)ﾉﾉ⌒●~*
******************************
boobs2
（·人·）
******************************
boombox1
♫♪.ılılıll|̲̅̅●̲̅̅|̲̅̅=̲̅̅|̲̅̅●̲̅̅|llılılı.♫♪
******************************
boombox2
♫♪ |̲̅̅●̲̅̅|̲̅̅=̲̅̅|̲̅̅●̲̅̅| ♫♪
******************************
bored
╭∩╮<-L->╭∩╮
******************************
boxing
ლ(•́•́ლ)
******************************
breakdown
ಥ﹏ಥ
******************************
british money
[£::]
******************************
bullshit
|3ᵕᶦᶦᶳᶣᶨᶵ
******************************
butt
(‿|‿)
******************************
butterfly
Ƹ̵̡Ӝ̵̨̄Ʒ
******************************
camera
[◉"]
******************************
car race
∙،°.  ˘Ô≈ôﺣ   » » »
******************************
careless
◔_◔
******************************
cassette1
|[●▪▪●]|
******************************
cassette2
[¯ↂ■■ↂ¯]
******************************
cat face
⦿⽘⦿
******************************
cat smile
≧◔◡◔≦﻿
******************************
cat2
龴ↀ◡ↀ龴
******************************
cat4
(=^ェ^=)
******************************
catlenny
( ͡° ᴥ ͡°)
******************************
chair
╦╣
******************************
chasing
''⌐(ಠ۾ಠ)¬''
******************************
cheer
  ^(¤o¤)^
******************************
cheers
（ ^_^）o自自o（^_^ ）
******************************
chess
♞▀▄▀▄♝▀▄
******************************
chess pieces
♚ ♛ ♜ ♝ ♞ ♟ ♔ ♕ ♖ ♗ ♘ ♙
******************************
chicken
ʚ(•｀
******************************
chu
(´ε｀ )
******************************
cigarette1
(̅_̅_̅_̅(̅_̅_̅_̅_̅_̅_̅_̅_̅̅_̅()ڪے
******************************
club bold
♣
******************************
club regular
♧
******************************
coffee now
{zzz}°°°( -_-)>c[_]
******************************
confused scratch
(⊙.☉)7
******************************
confused3
(°~°)
******************************
confused5
é_è
******************************
confused6
(˚ㄥ_˚)
******************************
confused7
(; ͡°_ʖ ͡°)
******************************
confused8
(´•_•`)
******************************
confused9
(ˇ_ˇ’!l)
******************************
crayons
((̲̅ ̲̅(̲̅C̲̅r̲̅a̲̅y̲̅o̲̅l̲̲̅̅a̲̅( ̲̅((>
******************************
crazy
ミ●﹏☉ミ
******************************
creeper
ƪ(ړײ)‎ƪ​​
******************************
cry
 (╯︵╰,)
******************************
cry face
｡ﾟ( ﾟஇ‸இﾟ)ﾟ｡
******************************
cry troll
༼ ༎ຶ ෴ ༎ຶ༽
******************************
crying1
Ỏ̷͖͈̞̩͎̻̫̫̜͉̠̫͕̭̭̫̫̹̗̹͈̼̠̖͍͚̥͈̮̼͕̠̤̯̻̥̬̗̼̳̤̳̬̪̹͚̞̼̠͕̼̠̦͚̫͔̯̹͉͉̘͎͕̼̣̝͙̱̟̹̩̟̳̦̭͉̮̖̭̣̣̞̙̗̜̺̭̻̥͚͙̝̦̲̱͉͖͉̰̦͎̫̣̼͎͍̠̮͓̹̹͉̤̰̗̙͕͇͔̱͕̭͈̳̗̭͔̘̖̺̮̜̠͖̘͓̳͕̟̠̱̫̤͓͔̘̰̲͙͍͇̙͎̣̼̗̖͙̯͉̠̟͈͍͕̪͓̝̩̦̖̹̼̠̘̮͚̟͉̺̜͍͓̯̳̱̻͕̣̳͉̻̭̭̱͍̪̩̭̺͕̺̼̥̪͖̦̟͎̻̰_Ỏ̷͖͈̞̩͎̻̫̫̜͉̠̫͕̭̭̫̫̹̗̹͈̼̠̖͍͚̥͈̮̼͕̠̤̯̻̥̬̗̼̳̤̳̬̪̹͚̞̼̠͕̼̠̦͚̫͔̯̹͉͉̘͎͕̼̣̝͙̱̟̹̩̟̳̦̭͉̮̖̭̣̣̞̙̗̜̺̭̻̥͚͙̝̦̲̱͉͖͉̰̦͎̫̣̼͎͍̠̮͓̹̹͉̤̰̗̙͕͇͔̱͕̭͈̳̗̭͔̘̖̺̮̜̠͖̘͓̳͕̟̠̱̫̤͓͔̘̰̲͙͍͇̙͎̣̼̗̖͙̯͉̠̟͈͍͕̪͓̝̩̦̖̹̼̠̘̮͚̟͉̺̜͍͓̯̳̱̻͕̣̳͉̻̭̭̱͍̪̩̭̺͕̺̼̥̪͖̦̟͎̻̰
******************************
cup1
(▓
******************************
cup2
\̅_̅/̷̚ʾ
******************************
cute cat
^⨀ᴥ⨀^
******************************
cute face
(｡◕‿◕｡)
******************************
cute face2
(ღ˘◡˘ღ)
******************************
cute face3
✿◕ ‿ ◕✿
******************************
cute face4
❀◕ ‿ ◕❀
******************************
cute face5
(✿◠‿◠)
******************************
cute face6
(◕‿◕✿)
******************************
cute face7
☾˙❀‿❀˙☽
******************************
cute face8
(◡‿◡✿)
******************************
cute face9
ლ(╹◡╹ლ)
******************************
dab
ヽ( •_)ᕗ
******************************
dalek
 ̵̄/͇̐\
******************************
damnyou
(ᕗ ͠° ਊ ͠° )ᕗ
******************************
dance2
♪♪ ヽ(ˇ∀ˇ )ゞ
******************************
dancee
♪┏(°.°)┛┗(°.°)┓┗(°.°)┛┏(°.°)┓ ♪
******************************
dancing
┌(ㆆ㉨ㆆ)ʃ
******************************
dancing people
‎(/.__.)/   \(.__.\)
******************************
dead eyes
¿ⓧ_ⓧﮌ
******************************
dear god why
щ（ﾟДﾟщ）
******************************
decorate
▂▃▅▇█▓▒░۩۞۩        ۩۞۩░▒▓█▇▅▃▂
******************************
depressed
(︶︹︶)
******************************
derp
ヘ（。□°）ヘ
******************************
devious smile
ಠ‿ಠ
******************************
dgaf
┌∩┐(◣ _ ◢)┌∩┐
******************************
diamond bold
♦
******************************
diamond regular
♢
******************************
disagree
٩◔̯◔۶
******************************
discombobulated
⊙﹏⊙
******************************
dislike1
(Ծ‸ Ծ)
******************************
dislike2
( ಠ ʖ̯ ಠ)
******************************
do you even lift bro?
ᕦ(ò_óˇ)ᕤ
******************************
double flip
┻━┻ ︵ヽ(`Д´)ﾉ︵﻿ ┻━┻
******************************
drowning
人人人ヾ( ;×o×)〃 人人人
******************************
drunkenness
ヽ（´ー｀）┌
******************************
dunno
¯\(°_o)/¯
******************************
dunno2
\_(シ)_/
******************************
dunno3
└㋡┘
******************************
dunno4
╘㋡╛
******************************
dunno5
٩㋡۶
******************************
electrocardiogram1
√v^√v^√v^√v^√♥
******************************
electrocardiogram2
v^v^v^v^√\/♥
******************************
electrocardiogram3
/\/\/\/\/\/\/\/\/\/\/\v^♥
******************************
electrocardiogram4
√v^√v^♥√v^√v^√
******************************
elephant
°j°m
******************************
emo dance
ヾ(-_- )ゞ
******************************
energy
 つ ◕_◕ ༽つ  つ ◕_◕ ༽つ
******************************
envelope
✉
******************************
epic gun
︻┳デ═—
******************************
equalizer
▇ ▅ █ ▅ ▇ ▂ ▃ ▁ ▁ ▅ ▃ ▅ ▅ ▄ ▅ ▇
******************************
error
(╯°□°)╯︵ ɹoɹɹƎ
******************************
exchange
(╯°□°）╯︵ ǝƃuɐɥɔxǝ
******************************
excited
☜(⌒▽⌒)☞
******************************
exorcism
ح(•̀ж•́)ง †
******************************
eye closed
 (╯_╰)
******************************
eye roll
⥀.⥀
******************************
eyes
℃ↂ_ↂↃ
******************************
face
•|龴◡龴|•
******************************
facepalm
(>ლ)
******************************
fail
o(╥﹏╥)o
******************************
fart
(ˆ⺫ˆ๑)<3
******************************
feel perky
(`･ω･´)
******************************
fido
V•ᴥ•V
******************************
fight
(ง'̀-'́)ง
******************************
finger1
╭∩╮(Ο_Ο)╭∩╮
******************************
finger2
┌∩┐(◣_◢)┌∩┐
******************************
finger3
ಠ︵ಠ凸
******************************
finger4
┌∩┐(>_<)┌∩┐
******************************
finn
| (• ◡•)|
******************************
fish invasion
›(̠̄:̠̄c  ›(̠̄:̠̄c  (¦Ҝ  (¦Ҝ  ҉  -  -  -   ¦̺͆¦  ▪▌
******************************
fish swim
¸.·´¯`·.´¯`·.¸¸.·´¯`·.¸><(((º>
******************************
fish3
`·.¸¸ ><((((º>.·´¯`·><((((º>
******************************
fisticuffs
ლ(｀ー´ლ)
******************************
flex
ᕙ(⇀‸↼‶)ᕗ
******************************
flip friend
(ノಠ ∩ಠ)ノ彡( \o°o)\
******************************
fly away
⁽⁽ଘ( ˊᵕˋ )ଓ⁾⁾
******************************
flying
ح˚௰˚づ
******************************
formula1 car
\ō͡≡o˞̶
******************************
french kiss
:-XÞ
******************************
frown
(ღ˘⌣˘ღ)
******************************
fu
(ಠ_ಠ)┌∩┐
******************************
fuck you2
(° ͜ʖ͡°)╭∩╮
******************************
fuckall
 	╭∩╮（︶︿︶）╭∩╮
******************************
fungry
Σ_(꒪ཀ꒪」∠)_
******************************
ghost
‹’’›(Ͼ˳Ͽ)‹’’›
******************************
gimme
༼ つ ◕_◕ ༽つ
******************************
glasses2
ᒡ◯ᵔ◯ᒢ
******************************
glitter
(*・‿・)ノ⌒*:･ﾟ✧
******************************
go away bear
╭∩╮ʕ•ᴥ•ʔ╭∩╮
******************************
gotit
(☞ﾟ∀ﾟ)☞
******************************
gtalk fit
(•̪̀●́)=ε/̵͇̿̿/'̿̿ ̿ ̿̿  N --------{---(@
******************************
guitar
c====(=#O| ) ~~ ♬·¯·♩¸¸♪·¯·♫¸
******************************
gun1
︻╦╤─
******************************
gun2
︻デ═一
******************************
gun3
╦̵̵̿╤─ ҉ ~ •
******************************
gun4
︻╦̵̵͇̿̿̿̿╤──
******************************
hairstyle
⨌⨀_⨀⨌
******************************
happy
 ۜ\(סּںסּَ` )/ۜ
******************************
happy birthday 1
ዞᏜ℘℘Ꮍ ℬℹℛʈዞᗬᏜᎽ
******************************
happy face
ヽ(´▽`)/
******************************
happy hug
\(ᵔᵕᵔ)/
******************************
happy square
【ツ】
******************************
happy10
(´ツ｀)
******************************
happy11
( ＾◡＾)っ
******************************
happy12
┏(＾0＾)┛┗(＾0＾)┓
******************************
happy13
(°⌣°)
******************************
happy14
٩(^‿^)۶
******************************
happy15
(•‿•)
******************************
happy16
ó‿ó
******************************
happy17
٩◔‿◔۶
******************************
happy18
ಠ◡ಠ
******************************
happy19
●‿●
******************************
happy2
⎦˚◡˚⎣
******************************
happy20
( '‿' )
******************************
happy21
^‿^
******************************
happy22
┌( ಠ‿ಠ)┘
******************************
happy23
(˘◡˘)
******************************
happy24
☯‿☯
******************************
happy25
\(• ◡ •)/
******************************
happy26
( ͡ʘ ͜ʖ ͡ʘ)
******************************
happy27
( ͡• ͜ʖ ͡• )
******************************
happy3
㋡
******************************
happy6
(ツ)
******************************
happy7
【シ】
******************************
happy8
㋛
******************************
happy9
(シ)
******************************
head shot
->~∑≥_≤)
******************************
heart bold
♥
******************************
heart regular
♡
******************************
heart1
»-(¯`·.·´¯)->
******************************
heart2
♡♡
******************************
hell yeah
(òÓ,)_\,,/
******************************
hello
(ʘ‿ʘ)╯
******************************
hello2
（ツ）ノ
******************************
help
٩(͡๏̯͡๏)۶
******************************
high five
( ⌒o⌒)人(⌒-⌒ )v
******************************
hitchhicking
(งツ)ว
******************************
honeycute
❤◦.¸¸.  ◦✿
******************************
house
__̴ı̴̴̡̡̡ ̡͌l̡̡̡ ̡͌l̡*̡̡ ̴̡ı̴̴̡ ̡̡͡|̲̲̲͡͡͡ ̲▫̲͡ ̲̲̲͡͡π̲̲͡͡ ̲̲͡▫̲̲͡͡ ̲|̡̡̡ ̡ ̴̡ı̴̡̡ ̡͌l̡̡̡̡.___
******************************
hug me
(っ◕‿◕)っ
******************************
hugger
(づ￣ ³￣)づ
******************************
huhu
█▬█ █▄█ █▬█ █▄█
******************************
hybrix
ʕʘ̅͜ʘ̅ʔ
******************************
i dont care
╭∩╮（︶︿︶）╭∩╮
******************************
i kill you
 ̿ ̿̿'̿̿\̵͇̿̿\=(•̪●)=/̵͇̿̿/'̿̿ ̿ ̿
******************************
im a hugger
(⊃｡•́‿•̀｡)⊃
******************************
injured
(҂◡_◡)
******************************
inlove
(✿ ♥‿♥)
******************************
innocent face
ʘ‿ʘ
******************************
japanese lion face
°‿‿°
******************************
jaymz
 (•̪●)==ε/̵͇̿​̿/’̿’̿ ̿ ̿̿    `(•.°)~
******************************
jazz musician
ヽ(⌐■_■)ノ♪♬
******************************
john lennon
((ºjº))
******************************
joker1
🂠
******************************
joker2
🃟
******************************
joker3
🃏
******************************
joker4
🃠
******************************
jokeranonimous
╭∩╮ (òÓ,) ╭∩╮
******************************
jokeranonimous2
╭∩╮(ô¿ô)╭∩╮
******************************
judgemental
\{ಠʖಠ\}
******************************
judging
( ఠ ͟ʖ ఠ)
******************************
kablewee
 	̿' ̿'\̵͇̿̿\з=( ͡ °_̯͡° )=ε/̵͇̿̿/'̿'̿ ̿
******************************
killer
(⌐■_■)--︻╦╤─ - - - (╥﹏╥)
******************************
kilroy was here
" Ü "
******************************
kirby
(つ -‘ _ ‘- )つ
******************************
kiss2
(︶ε︶メ)
******************************
kiss3
╮(︶ε︶メ)╭
******************************
kissing
( ˘ ³˘)♥
******************************
kissing2
( ˘з˘)ε˘`)
******************************
kissing3
(~˘з˘)~~(˘ε˘~)
******************************
kissing4
(っ˘з(O.O )♥
******************************
kissing5
(`˘з(•˘⌣˘•)
******************************
kissing6
(っ˘з(˘⌣˘ )
******************************
kitty emote
ᵒᴥᵒ#
******************************
kokain
 ̿ ̿' ̿'\̵͇̿̿\з=(•̪●)=ε/̵͇̿̿/'̿''̿ ̿
******************************
kyubey
／人 ⌒ ‿‿ ⌒ 人＼
******************************
kyubey2
／人 ◕‿‿◕ 人＼
******************************
laughing
(＾▽＾)
******************************
lenny
( ͡° ͜ʖ ͡°)
******************************
line brack
●▬▬▬▬๑۩۩๑▬▬▬▬▬●
******************************
listening to headphones
◖ᵔᴥᵔ◗ ♪ ♫
******************************
loading1
█▒▒▒▒▒▒▒▒▒
******************************
loading2
███▒▒▒▒▒▒▒
******************************
loading3
█████▒▒▒▒▒
******************************
loading4
███████▒▒▒
******************************
loading5
█████████▒
******************************
loading6
██████████
******************************
looking face
ô¿ô
******************************
love
ⓛⓞⓥⓔ
******************************
love in my eye1
(♥_♥)
******************************
love in my eye2
(｡❤◡❤｡)
******************************
love in my eye3
(❤◡❤)
******************************
love2
 ~♡ⓛⓞⓥⓔ♡~
******************************
love3
♥‿♥
******************************
love4
(Ɔ ˘⌣˘)♥(˘⌣˘ C)
******************************
mad
òÓ
******************************
mad10
(•ˋ _ ˊ•)
******************************
mad2
(ノ`Д ́)ノ
******************************
mad5
Ծ_Ծ
******************************
mad6
⋋_⋌
******************************
mad7
(ノ≥∇≤)ノ
******************************
mad9
ƪ(`▿▿▿▿´ƪ)
******************************
man spider
/╲/\༼ *ಠ 益 ಠ* ༽/\╱\
******************************
man tears
ಥ_ಥ
******************************
mango
) _ _ __/°°¬
******************************
med
ب_ب
******************************
med man
(⋗_⋖)
******************************
meditation
‿( ́ ̵ _-`)‿
******************************
meep
\(°^°)/
******************************
meow
ฅ^•ﻌ•^ฅ
******************************
monocle
(╭ರ_•́)
******************************
monster
٩(̾●̮̮̃̾•̃̾)۶
******************************
monster2
٩(- ̮̮̃-̃)۶
******************************
mouse2
 .       ~~(__^·>
******************************
mouse3
<·^__)~~          .
******************************
mouse4
—-{,_,”><",_,}----
******************************
myancat
mmmyyyyy<⦿⽘⦿>aaaannn
******************************
nathan
♪└(￣◇￣)┐♪└(￣◇￣)┐♪└(￣◇￣)┐♪
******************************
needle1
┣▇▇▇═─
******************************
neo
(⌐■_■)--︻╦╤─ - - -
******************************
no support
乁( ◔ ౪◔)「      ┑(￣Д ￣)┍
******************************
nose
\˚ㄥ˚\
******************************
opera
ヾ(´〇`)ﾉ♪♪♪
******************************
owlkin
(ᾢȍˬȍ)ᾢ ļ ļ ļ ļ ļ
******************************
pac man
ᗧ···ᗣ···ᗣ··
******************************
panda
ヽ(￣(ｴ)￣)ﾉ
******************************
party time
┏(-_-)┛┗(-_-﻿ )┓┗(-_-)┛┏(-_-)┓
******************************
peace yo!
(‾⌣‾)♉
******************************
peepers
ಠಠ
******************************
penis2
○○)=======o)
******************************
perky
( ๏ Y ๏ )
******************************
pig2
༼☉ɷ⊙༽
******************************
piggy
(∩◕(oo)◕∩)
******************************
ping pong
( •_•)O*¯`·.¸.·´¯`°Q(•_• )
******************************
pirate
✌(◕‿-)✌
******************************
pistols1
¯¯̿̿¯̿̿'̿̿̿̿̿̿̿'̿̿'̿̿̿̿̿'̿̿̿)͇̿̿)̿̿̿̿ '̿̿̿̿̿̿\̵͇̿̿\=(•̪̀●́)=o/̵͇̿̿/'̿̿ ̿ ̿̿
******************************
pistols2
̿' ̿'\̵͇̿̿\з=(◕_◕)=ε/̵͇̿̿/'̿'̿ ̿
******************************
pistols3
 ̿̿ ̿̿ ̿’̿̿’̿\̵͇̿̿\з=( ͡ °_̯͡° )=ε/̵͇̿̿/’̿̿’̿ ̿ ̿̿ ̿̿
******************************
pistols4
(•̪●)=ε/̵͇̿̿/’̿’̿ ̿ ̿̿ ̿ ̿””
******************************
pistols5
̿̿ ̿̿ ̿̿ ̿'̿'\̵͇̿̿\з=( ͠° ͟ʖ ͡°)=ε/̵͇̿̿/'̿̿ ̿ ̿ ̿ ̿ ̿
******************************
playing cards
[♥]]] [♦]]] [♣]]] [♠]]]
******************************
playing cards clubs
[♣]]]
******************************
playing cards clubs waterfall
🃏🃑🃒🃓🃔🃕🃖🃗🃘🃙🃚🃛🃜🃝🃞
******************************
playing cards diamonds
[♦]]]
******************************
playing cards diamonds waterfall
🃟🃁🃂🃃🃄🃅🃆🃇🃈🃉🃊🃋🃌🃍🃎
******************************
playing cards hearts
[♥]]]
******************************
playing cards hearts waterfall
🂠🂱🂲🂳🂴🂵🂶🂷🂸🂹🂺🂻🂼🂽🂾
******************************
playing cards spades
[♠]]]
******************************
playing cards spades waterfall
🃠🂡🂢🂣🂤🂥🂦🂧🂨🂩🂪🂫🂬🂭🂮
******************************
playing cards waterfall
🂱🂲🂳🂴🂵🂶🂷🂸🂹🂺🂻🂼🂽🂾🃁🃂🃃🃄🃅🃆🃇🃈🃉🃊🃋🃌🃍🃎🃑🃒🃓🃔🃕🃖🃗🃘🃙🃚🃛🃜🃝🃞🂡🂢🂣🂤🂥🂦🂧🂨🂩🂪🂫🂬🂭🂮🂠🃏🃟
******************************
playing cards waterfall (trump)
🃠🃡🃢🃣🃤🃥🃦🃧🃨🃩🃪🃫🃬🃭🃮🃯🃰🃱🃲🃳🃴🃵
******************************
playing in snow
(╯^□^)╯︵ ❄☃❄
******************************
point
(☞ﾟヮﾟ)☞
******************************
polar bear
ˁ˚ᴥ˚ˀ
******************************
pretty eyes
ఠ_ఠ
******************************
professor
"""⌐(ಠ۾ಠ)¬"""
******************************
puls
––•–√\/––√\/––•––
******************************
put the table back
┬─┬﻿ ノ( ゜-゜ノ)
******************************
rak
/⦿L⦿\
******************************
rare
┌ಠ_ಠ)┌∩┐ ᶠᶸᶜᵏ♥ᵧₒᵤ
******************************
real face
( ͡° ͜ʖ ͡°)﻿
******************************
religious
☪ ✡ † ☨ ✞ ✝ ☥ ☦ ☓ ♁ ☩
******************************
resting my eyes
ᴖ̮ ̮ᴖ
******************************
robber
 -╤╗_(◙◙)_╔╤-   -  -  -  \o/ \o/ \o/
******************************
robot boy
◖(◣☩◢)◗
******************************
robot2
 c[○┬●]כ
******************************
robot3
{•̃_•̃}
******************************
rocket
∙∙∙∙∙·▫▫ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ☼)===>
******************************
rope
╚(▲_▲)╝
******************************
round bird
,(u°)>
******************************
running
ε=ε=ε=┌(;*´Д`)ﾉ
******************************
sad and confused
¯\_(⊙︿⊙)_/¯
******************************
sad and crying
(ᵟຶ︵ ᵟຶ)
******************************
sad face
(ಥ⌣ಥ)
******************************
sad1
ε(´סּ︵סּ`)з
******************************
sad2
(✖╭╮✖)
******************************
sad3
(◑﹏◐)
******************************
sad4
(◕_◕)
******************************
sad5
(´ᗣ｀)
******************************
sat
'(◣_◢)'
******************************
satan
↑_(ΦwΦ;)Ψ
******************************
satisfied
(◠﹏◠)
******************************
scissors
✄
******************************
seal
(ᵔᴥᵔ)
******************************
sex symbol
◢♂◣◥♀◤◢♂◣◥♀◤
******************************
shark face
( ˇ෴ˇ )
******************************
sheep
Â°lÂ°(,,,,);
******************************
shocked1
(∩╹□╹∩)
******************************
shrug
¯\_(ツ)_/¯
******************************
shy
(๑•́ ₃ •̀๑)
******************************
singing
d(^o^)b¸¸♬·¯·♩¸¸♪·¯·♫¸¸
******************************
singing2
♪└(￣◇￣)┐♪
******************************
sky free
ѧѦ ѧ  ︵͡︵  ̢ ̱ ̧̱ι̵̱̊ι̶̨̱ ̶̱   ︵ Ѧѧ  ︵͡ ︵   ѧ Ѧ    ̵̗̊o̵̖  ︵  ѦѦ ѧ
******************************
sleepy
눈_눈
******************************
sleepy coffee
( -_-)旦~
******************************
slenderman
ϟƖΣNd€RMαN
******************************
smooth
(づ ￣ ³￣)づ ⓈⓂⓄⓄⓉⒽ
******************************
smug bastard
(‾⌣‾)
******************************
sniper rifle
︻デ┳═ー
******************************
sniperstars
✯╾━╤デ╦︻✯
******************************
snowing
✲´*。.❄¨¯`*✲。❄。*。
******************************
snowman1
☃
******************************
sorreh bro
(◢_◣)
******************************
spade bold
♠
******************************
spade regular
♤
******************************
sparkling heart
-`ღ´-
******************************
spell cast
╰( ⁰ ਊ ⁰ )━☆ﾟ.*･｡ﾟ
******************************
spider4
/╲/\╭ºoꍘoº╮/\╱\
******************************
squee
ヾ(◎o◎,,；)ﾉ
******************************
squid
くコ:彡
******************************
srs face
(ಠ_ಠ)
******************************
staring
٩(๏_๏)۶
******************************
stars
✌⊂(✰‿✰)つ✌
******************************
stars2
⋆ ✢ ✣ ✤ ✥ ✦ ✧ ✩ ✪ ✫ ✬ ✭ ✮ ✯ ✰ ★
******************************
stranger danger
(づ｡◕‿‿◕｡)づ
******************************
strut
ᕕ( ᐛ )ᕗ
******************************
stunna shades
(っ▀¯▀)つ
******************************
sunglasses1
(•_•)>⌐■-■ (⌐■_■)
******************************
sunny day
☁ ▅▒░☼‿☼░▒▅ ☁
******************************
surprised10
(⊙.◎)
******************************
surprised11
๏_๏
******************************
surprised12
(˚-˚)
******************************
surprised13
˚o˚
******************************
surprised15
( ﾟoﾟ)
******************************
surprised16
◉_◉
******************************
surprised17
【•】_【•】
******************************
surprised18
(•ิ_•)
******************************
surprised19
⊙⊙
******************************
surprised2
（　ﾟДﾟ）
******************************
surprised20
͡๏_͡๏
******************************
surprised4
(º_•)
******************************
surprised5
(º.º)
******************************
surprised6
⊙▃⊙
******************************
surprised8
●_●
******************************
surprised9
(⊙̃.o)
******************************
swim
＿（ッ）>＿/／
******************************
swim2
ー（ッ）」
******************************
swim3
＿（ッ）へ
******************************
sword2
▬▬ι═══════ﺤ    -═══════ι▬▬
******************************
sword3
ס₪₪₪₪§|(Ξ≥≤≥≤≥≤ΞΞΞΞΞΞΞΞΞΞ>
******************************
sword8
▬▬ι═══════>
******************************
sword9
 <═══════ι▬▬
******************************
table flip
(╯°□°）╯︵ ┻━┻
******************************
table flip10
(/ .□.)\ ︵╰(゜Д゜)╯︵ /(.□. \)
******************************
table flip2
(ﾉಥ益ಥ）ﾉ﻿ ┻━┻
******************************
table flip3
┬─┬ノ( º _ ºノ)
******************************
table flip4
(ノಠ益ಠ)ノ彡┻━┻
******************************
table flip5
┬──┬﻿ ¯\_(ツ)
******************************
table flip6
┻━┻ ︵﻿ ¯\(ツ)/¯ ︵ ┻━┻
******************************
table flip7
(╯°□°)╯︵ ┻━┻ ︵ ╯(°□° ╯)
******************************
table flip8
(╯°Д°）╯︵ /(.□ . \)
******************************
table flip9
(ノ^_^)ノ┻━┻ ┬─┬ ノ( ^_^ノ)
******************************
taking a dump
(⩾﹏⩽)
******************************
teddy
ˁ(⦿ᴥ⦿)ˀ
******************************
telephone
ε(๏_๏)з】
******************************
tgif
“ヽ(´▽｀)ノ”
******************************
things that can_t be unseen
♨_♨
******************************
tidy up
┬─┬⃰͡ (ᵔᵕᵔ͜ )
******************************
tie-fighter
|—O—|
******************************
tired
( ͡ಠ ʖ̯ ͡ಠ)
******************************
touchy feely
ԅ(≖‿≖ԅ)
******************************
toungue out1
:-Þ
******************************
train
/˳˳_˳˳\!˳˳X˳˳!(˳˳_˳˳)[˳˳_˳˳]
******************************
tripping out
q(❂‿❂)p
******************************
trolling
༼∵༽ ༼⍨༽ ༼⍢༽ ༼⍤༽
******************************
umadbro
¯\_(ツ)_/¯
******************************
up
(◔/‿\◔)
******************************
upset
◤(¬‿¬)◥
******************************
upsidedown
( ͜。 ͡ʖ ͜。)
******************************
wat
ಠ_ಠ
******************************
wat-wat
Σ(‘◉⌓◉’)
******************************
waves
°º¤ø,¸¸,ø¤º°`°º¤ø,¸,ø¤°º¤ø,¸¸,ø¤º°`°º¤ø,¸
******************************
weather
☼ ☀ ☁ ☂ ☃ ☄ ☾ ☽ ❄ ☇ ☈ ⊙ ☉ ℃ ℉ ° ❅ ✺ ϟ
******************************
what?
ة_ة
******************************
what??
(Ͼ˳Ͽ)..!!!
******************************
whisling
(っ•́｡•́)♪♬
******************************
why
ლ( `Д’ ლ)
******************************
winnie the pooh
ʕ •́؈•̀)
******************************
winning
(•̀ᴗ•́)و ̑̑
******************************
wizard
(∩ ͡° ͜ʖ ͡°)⊃━☆ﾟ. *
******************************
wizard2
(∩｀-´)⊃━☆ﾟ.*･｡ﾟ
******************************
woman
▓⚗_⚗▓
******************************
worried
(´･_･`)
******************************
wtf dude?
＼(◑д◐)＞∠(◑д◐)
******************************
yawning
＼(＾o＾)／
******************************
yessir
∠(･`_´･ )
******************************
yolo
Yᵒᵘ Oᶰˡʸ Lᶤᵛᵉ Oᶰᶜᵉ
******************************
yun
(っ˘ڡ˘ς)
******************************
zable
ಠ_ರೃ
******************************
zoidberg
(\/)(Ö,,,,Ö)(\/)
******************************
zombie
'º_º'
******************************
zombie2
[¬º-°]¬
******************************
zoned
(⊙_◎)
******************************
>>> tprint("test1\ntest2\ntest3","fancy42")
ţ€$ţ1
ţ€$ţ2
ţ€$ţ3
>>> tprint("test1\ntest2\ntest3\n","fancy44")
тεƨт1
тεƨт2
тεƨт3
<BLANKLINE>
>>> tprint("","fancy44")
<BLANKLINE>
>>> tprint("\n","fancy44")
<BLANKLINE>
<BLANKLINE>
>>> tprint("\n\n","fancy44")
<BLANKLINE>
<BLANKLINE>
<BLANKLINE>
>>> text2art("",font="fancy44")
''
>>> decor_list()
angry1
  ୧༼ಠ益ಠ༽︻╦╤─ƭεรƭ
******************************
arrow1
➶➶➶➶➶ƭεรƭ➷➷➷➷➷
******************************
arrow2
↤↤↤↤↤ƭεรƭ↦↦↦↦↦
******************************
arrow3
↫↫↫↫↫ƭεรƭ↬↬↬↬↬
******************************
arrow4
–--´¯`----»ƭεรƭ«----´¯`–--
******************************
arrow5
‹—•°º¤°(ƭεรƭ)°¤º°•—›
******************************
arrow6
⇇⇇⇇ƭεรƭ⇉⇉⇉
******************************
arrow7
⇉⇉⇉ƭεรƭ⇇⇇⇇
******************************
arrow8
]|I{•------» [ƭεรƭ] «------•}I|[
******************************
arrow_wave1
(¯`·.¸¸.·´¯`·.¸¸.-> [ƭεรƭ<-.¸¸.·´¯`·.¸¸.·´¯)
******************************
arrow_wave2
°·.¸.·°¯°·.¸.·°¯°·.¸.-> [ƭεรƭ] <-.¸.·°¯°·.¸.·°¯°·.¸.·°
******************************
ball1
●●--●●--●●ƭεรƭ●●--●●--●●
******************************
ball2
··●(`●-ƭεรƭ-●´)●··
******************************
ball3
●·(¯¨°¹●.ƭεรƭ.●¹°¨¯)·●
******************************
barcode1
▌│█║▌║▌║ ƭεรƭ ║▌║▌║█│▌
******************************
bazar1
[ꔊꔊꔊ[🍉]ƭεรƭ[🍓]ꔊꔊꔊ]
******************************
block1
■■■■■■■■□□□ƭεรƭ□□□■■■■■■■■
******************************
block2
▄▀▀▄▀▀▄ƭεรƭ▄▀▀▄▀▀▄
******************************
bow1
---»--@-}ƭεรƭ{-@--«---
******************************
bubble
ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ▫▫·∙∙∙∙∙ƭεรƭ∙∙∙∙∙·▫▫ᵒᴼᵒ▫ₒₒ▫ᵒᴼᵒ
******************************
cat1
｢(◔ω◔「)三ƭεรƭ三三三ʅ(；◔౪◔)ʃ
******************************
cat2
☆(ﾉ^o^)ﾉ‥‥ƭεรƭ‥…━━━━〇(^~^)
******************************
cat3
＼＼\(۶•̀ᴗ•́)۶//／／ƭεรƭ\\٩(•́⌄•́๑)و////
******************************
cell1
╔╣█╠╗╚ƭεรƭ╝╔╣█╠╗
******************************
champion1
◄[🏆]► ƭεรƭ ◄[🥇]►
******************************
chess1
▀▄▀▄▀▄ƭεรƭ▄▀▄▀▄▀
******************************
confused1
¯\_(ツ)_/¯ƭεรƭ¯\_(ツ)_/¯
******************************
confused2
¨°o.OƭεรƭO.o°¨
******************************
cross1
✖✖✖✖ƭεรƭ✖✖✖✖
******************************
depressed
‿︵‿︵(ಥ﹏ಥ)‿︵‿︵ƭεรƭ
******************************
diamon3
¯`-●=»>◆<«=●ƭεรƭ●=»>◆<«=●-´¯
******************************
diamond1
◄]·♦·»ƭεรƭ«·♦·[►
******************************
diamond2
-·●·♦^v¯`♦)ƭεรƭ(♦¯`v^♦·●·-
******************************
egypt1
╭₪₪₪ƭεรƭ₪₪₪╮
******************************
emotions1
╱(●_●)(^_^)ƭεรƭ(^_^)(●_●)╲
******************************
fancy1
╰⊱♥⊱╮ღ꧁ƭεรƭ꧂ღ╭⊱♥≺
******************************
fancy10
჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ჻ƭεรƭ჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ༉ ༘჻ღཾཿ჻
******************************
fancy11
☆彡彡ƭεรƭミミ⛧
******************************
fancy12
★彡彡ƭεรƭミミ★
******************************
fancy13
»»——☠——«ƭεรƭ»——☠——««
******************************
fancy14
 ★━━─ƭεรƭ─━━★
******************************
fancy15
■━■━■━■ƭεรƭ■━■━■━■
******************************
fancy16
└╏ ･ƭεรƭ･ ╏┐
******************************
fancy17
●～●～ƭεรƭ～●～●
******************************
fancy18
((¯*.».( ¯*♥ »ƭεรƭ« ♥* ¯).«.*¯))
******************************
fancy19
✧∭✧∰✧∭✧ƭεรƭ✧∭✧∰✧∭✧
******************************
fancy2
★彡ƭεรƭ彡★
******************************
fancy20
●▬▬▬๑۩۩๑▬▬▬●ƭεรƭ●▬▬▬๑۩۩๑▬▬▬●
******************************
fancy21
(¯`·._.·ƭεรƭ·._.·´¯)
******************************
fancy22
×÷·.·´¯`·)»ƭεรƭ«(·´¯`·.·÷×
******************************
fancy23
· ··^v´¯`×)ƭεรƭ(×´¯`v^·· ·
******************************
fancy24
.·´¯`·->ƭεรƭ<-·´¯`·.
******************************
fancy25
- - --^[ƭεรƭ]^-- - -
******************************
fancy26
(¯`·»o«·´¯`·>ƭεรƭ<·´¯`·»o«·´¯)
******************************
fancy27
).•ˆ•+*¨ƭεรƭ¨*+•ˆ•.(
******************************
fancy28
•·.·¯`·.·•ƭεรƭ•·.·¯`·.·•
******************************
fancy29
•·.·´¯`·.·•ƭεรƭ•·.·´¯`·.·•
******************************
fancy3
·٠•●♥ Ƹ̵̡Ӝ̵̨̄Ʒ ♥●•٠·˙ƭεรƭ˙·٠•●♥ Ƹ̵̡Ӝ̵̨̄Ʒ ♥●•٠·˙
******************************
fancy30
(¯`·._)ƭεรƭ(¯`·._)
******************************
fancy31
·°¯`·•ƭεรƭ•·´¯°·
******************************
fancy32
.ҳ̸Ҳ̸Ҳ̸ҳ•.•´¯`•ƭεรƭ•´¯`•.•..ҳ̸Ҳ̸Ҳ̸ҳ.
******************************
fancy33
×º°”˜`”°º×ƭεรƭ×º°”˜`”°º×
******************************
fancy34
•]•·´º´·»ƭεรƭ«·´º´·•[•
******************************
fancy35
-=₪۩۞۩₪=ƭεรƭ=₪۩۞۩₪=-
******************************
fancy36
°•. °•. °•.ƭεรƭ.•° .•° .•°
******************************
fancy37
(`·.·•ƭεรƭ•·.·')
******************************
fancy38
+*¨^¨*+ƭεรƭ+*¨^¨*+
******************************
fancy39
-·=»◆‡«=·-ƭεรƭ-·=»◆‡«=·-
******************************
fancy4
٩(●̮̮̃•̃)=/̵͇̿̿/'̿̿ ̿̿ƭεรƭ ̿̿ ̿̿ ̿̿\̵͇̿̿\=(•̃●̮̮̃)۶
******************************
fancy40
•°o.OƭεรƭO.o°•
******************************
fancy41
©º°¨¨°º©ƭεรƭ©º°¨¨°º©
******************************
fancy42
°·.¸.·°¯°·.¸.-<ƭεรƭ>-.¸.·°¯°·.¸.·°
******************************
fancy43
•°l¯l_l¯l*ƭεรƭ*l¯l_l¯l•°
******************************
fancy44
—(•·÷[ƭεรƭ]÷·•)—
******************************
fancy45
·ï¡÷¡ï·ƭεรƭ·ï¡÷¡ï·
******************************
fancy46
εїзƭεรƭεїз
******************************
fancy47
•°•.•°•ƭεรƭ•°•.•°•
******************************
fancy48
|¯|_|¯|_.°•. °•.->ƭεรƭ<-.•° .•°._|¯|_|¯|
******************************
fancy49
¸.´)(`·[ƭεรƭ]·´)(` .¸
******************************
fancy5
◇─◇──◇────◇ƭεรƭ◇─────◇──◇─◇
******************************
fancy50
•]•·✦º✦·»ƭεรƭ«·✦º✦·•[•
******************************
fancy51
;`';_._;`';_.ƭεรƭ._;'`;_._.'`;
******************************
fancy52
••.•´¯`•.••ƭεรƭ••.•´¯`•.••
******************************
fancy53
|!¤*'~``~'*¤!||ƭεรƭ||!¤*'~``~'*¤!|
******************************
fancy54
°•.•°o.OƭεรƭO.o°•.•°•
******************************
fancy55
––––º•(-ºƭεรƭº-)•º––––
******************************
fancy56
ҳ̸Ҳ̸Ҳ̸ҳ(¯`·.•.ƭεรƭ.•.·´¯)ҳ̸Ҳ̸Ҳ̸ҳ
******************************
fancy57
◄°l||l°ƭεรƭ°l||l°►
******************************
fancy58
·∙·÷±‡±:∙ƭεรƭ∙:±‡±÷·∙·
******************************
fancy59
(¯`v^÷••ƭεรƭ••÷^v´¯)
******************************
fancy6
×º°”˜`”°º× [ƭεรƭ] ×º°”˜`”°º×
******************************
fancy60
ø´¯`·¤»ƭεรƭ«¤·´¯`ø
******************************
fancy61
º¯`·.·•ƭεรƭ•·.·´¯º
******************************
fancy62
«(.·°¯`·->ƭεรƭ<-·´¯°·.)»
******************************
fancy63
‹–…·´`·…–›ƭεรƭ‹–…·´`·…–›
******************************
fancy64
.·´`·.·´¯`·.›ƭεรƭ‹.·´¯`·.·´`·.
******************************
fancy65
(¯(_(¯`•ƭεรƭ•´¯)_)¯)•
******************************
fancy66
[.·´¯`·.]ƭεรƭ[.·´¯`·.]
******************************
fancy67
ì..·´`•ƭεรƭ•´`·..í
******************************
fancy68
¯°•º¤◆¤º°¯ƭεรƭ¯°º¤◆¤º•°¯
******************************
fancy69
<.•ˆ•…ƭεรƭ…•ˆ•.>
******************************
fancy7
ڿڰۣ—☸ڿڰۣ—✨🌺✨ƭεรƭ✨🌺✨—ڿڰۣ—☸ڿڰۣ
******************************
fancy70
‹‹¡Ì›–•¦[ƭεรƭ]¦•–‹Ì¡››
******************************
fancy71
¯`·.__.·´¯»ƭεรƭ«¯`·.__.·´¯
******************************
fancy72
‹v^v^v^•ƭεรƭ•^v^v^v›
******************************
fancy73
{¯`·._.• .·´«ƭεรƭ»`·.•._.·´¯}
******************************
fancy74
´¯`*¤.·´`·.¤ƭεรƭ¤.·´`·.¤*´¯`
******************************
fancy75
‹—•((.•ˆ•…ƭεรƭ…•ˆ•.))•—›
******************************
fancy76
÷(`•´`·.ƭεรƭ.·´`•´)÷
******************************
fancy77
»-(¯`v´¯)-»ƭεรƭ«-(¯`v´¯)-«
******************************
fancy78
¸„.-•~¹°”ˆ˜¨ƭεรƭ¨˜ˆ”°¹~•-.„¸
******************************
fancy79
-漫~*'¨¯¨'*·舞~ƭεรƭ~舞*'¨¯¨'*·~漫-
******************************
fancy8
·°¯`·•🌹■🌼■🌸ꕥƭεรƭꕥ🌸■🌼■🌹•·´¯°·
******************************
fancy80
◦•●◉✿ƭεรƭ✿◉●•◦
******************************
fancy81
ヾ(>u<●【*:..｡o○ƭεรƭ○o。..:*】●>u<)ﾉ
******************************
fancy82
[]ðº°˜¨˜°ºð[]ƭεรƭ[]ðº°˜¨˜°ºð[]
******************************
fancy83
●¸.•*¨Ƹ̵̡Ӝ̵̨̄Ʒ¨*•.¸●ƭεรƭ●¸.•*¨Ƹ̵̡Ӝ̵̨̄Ʒ¨*•.¸●
******************************
fancy84
.--ღஐƸ̵̡Ӝ̵̨̄Ʒஐღ--.ƭεรƭ.--ღஐƸ̵̡Ӝ̵̨̄Ʒஐღ--.
******************************
fancy85
⊱━━━.⋅εïз⋅.━━━⊰ƭεรƭ⊱━━━.⋅εïз⋅.━━━⊰
******************************
fancy86
((*´_●｀☆ﾟ+.ƭεรƭ.+ﾟ☆´●_｀*))
******************************
fancy87
꧁༺ƭεรƭ༻꧂
******************************
fancy88
𒆜ƭεรƭ𒆜
******************************
fancy89
ᕚ(ƭεรƭ)ᕘ
******************************
fancy9
(◦′ᆺ‵◦) ♬° ✧❥✧¸.•*¨*✧♡✧ƭεรƭ✧♡✧*¨*•.❥
******************************
fancy90
★彡(ƭεรƭ)彡★
******************************
fancy91
◤✞ƭεรƭ✞◥
******************************
fancy92
██▓▒­░⡷⠂ƭεรƭ⠐⢾░▒▓██
******************************
fancy93
☆꧁✬◦°˚°◦. ƭεรƭ.◦°˚°◦✬꧂☆
******************************
fancy94
▞▞▞▞▞▖ƭεรƭ▝▞▞▞▞▞
******************************
fancy95
𓂀ƭεรƭ𓂀
******************************
flame1
ıllıllıƭεรƭıllıllı
******************************
flower1
꧁✿🌸╭⊱ƭεรƭ⊱╮🌸✿꧂
******************************
food1
╭₪🍔₪🍟₪ƭεรƭ₪🍟₪🍔₪╮
******************************
food2
︾︽🍍︾︽ ƭεรƭ︽︾🍍︽︾
******************************
haha
⚡️¯\_༼ ಥ ‿ ಥ ༽_/¯⚡ƭεรƭ
******************************
happy1
o͡͡͡͡͡͡͡͡͡͡͡͡͡͡╮(｡❛ᴗ❛｡)╭o͡͡͡͡͡͡͡͡͡͡͡͡͡͡ƭεรƭo͡͡͡͡͡͡͡͡͡͡͡͡͡͡╮(｡❛ᴗ❛｡)╭o͡͡͡͡͡͡͡͡͡͡͡͡͡͡
******************************
happy_new_year
❸ ❷ ❶ 🥂҉  ̡ ۫۰ Ձ٥۫١9 ۪ ҉   ۫۰̡´ƭεรƭ⭐️
******************************
hawaii1
~-<🌴>-~ƭεรƭ~-<☀️>-~
******************************
hawaii2
✈️─═∙∙∙▫▫ᵒᴼᵒ▫._ƭεรƭ▫▫..🌴
******************************
heart1
~~<💚>~~ƭεรƭ~~<💚>~~
******************************
heart10
ミ💖ƭεรƭ💖彡
******************************
heart11
෴❤️෴ ෴❤️෴ƭεรƭ෴❤️෴ ෴❤️෴
******************************
heart12
💖´ *•.¸♥¸.•**ƭεรƭ**•.¸♥¸.•*´💖
******************************
heart13
❤♡❤♡❤♡❤♡❤♡❤♡❤♡ƭεรƭ♡❤♡❤♡❤♡❤♡❤♡❤♡❤
******************************
heart14
❤꧁ღ⊱♥ƭεรƭ♥⊱ღ꧂❤
******************************
heart15
ミミ◦❧◦°˚°◦.¸¸◦°´❤*•.¸♥ƭεรƭ♥¸.•*❤´°◦¸¸.◦°˚°◦☙◦彡彡
******************************
heart2
~~><~~>💖ƭεรƭ💖<~~><~~
******************************
heart3
─═ڿڿۣڿ═─💖─═ڿڿۣڿ═─ƭεรƭ─═ڿڿۣڿ═─💖─═ڿڿۣڿ═─
******************************
heart4
༺♥༻❀༺♥༻💕﻿ƭεรƭ💕﻿༺♥༻❀༺♥༻
******************************
heart5
💗💜.¸¸.•´¯`☆ºஇ•´♥ƭεรƭ♥•´இº☆.¸¸.•´¯`💜💗
******************************
heart6
-♥-♡--^[ƭεรƭ]^--♡-♥-
******************************
heart7
*•.¸♡ƭεรƭ♡¸.•*
******************************
heart8
╚»♡«╝ƭεรƭ╚»♡«╝
******************************
heart9
´*•.¸(*•.¸♥¸.•*´)¸.•*´ƭεรƭ´*•.¸(*•.¸♥¸.•*´)¸.•*´
******************************
line1
┏━━━━━━┛ƭεรƭ┗━━━━━━┓
******************************
line2
╭─━━━━━─╯ƭεรƭ╰─━━━━━─╮
******************************
line3
┕━━━━ ⋆⋅☆⋅⋆ ━━┑ƭεรƭ┍━━ ⋆⋅☆⋅⋆ ━━━━┙
******************************
line4
┕━━━━✿━━┑ƭεรƭ┍━━✿━━━━┙
******************************
love_music
¸.•*¨*•.¸♪¸.•*¨*•.¸♥¸.•*¨*•.¸ƭεรƭ¸.•*¨*•.¸♥¸.•*¨*•.¸♪¸.•*¨*•.¸
******************************
lucky1
🌈ꔣᨐ ƭεรƭ ᨐꔣ🌈
******************************
missile1
💢🚀🚀💢ƭεรƭ💢🚀🚀💢
******************************
mountain1
人人人ƭεรƭ人人人
******************************
mountain2
╱╲╱╳╲╱╲ƭεรƭ╱╲╱╳╲╱╲
******************************
mountain3
ᨏᨐᨓƭεรƭᨓᨐᨏ
******************************
music1
🎧♪┏(°.°)┛🎼ƭεรƭ🎼┏(°.°)┛♪🎧
******************************
music2
♪•]•·º♫·》»ƭεรƭ«《·♫º·•[•♪
******************************
music3
·.¸¸.·♩♪♫ƭεรƭ♫♪♩·.¸¸.·
******************************
pencil1
✎✐✎✐✎ƭεรƭ✐✎✐✎✐
******************************
poker1
._|.<(+_+)>.|_. [ƭεรƭ] ._|.<(+_+)>.|_.
******************************
puzzle1
╚╝╔╗╚╝╔╗ƭεรƭ╚╝╔╗╚╝╔╗
******************************
puzzle2
╭╮╰╯╭╮╰╯ƭεรƭ╭╮╰╯╭╮╰╯
******************************
puzzle3
︹︺︹︺ƭεรƭ︺︹︺︹
******************************
sad1
(-_-) ƭεรƭ (-_-)
******************************
sad2
ԅ[ •́ ﹏├┬┴┬┴ƭεรƭԅ[ •́ ﹏ •̀ ]و
******************************
sad3
(^^(-_-)^^)ƭεรƭ(^^(-_-)^^)
******************************
sad4
(-_-) [ƭεรƭ] (-_-)
******************************
smile1
`•.,¸¸,.•´¯ƭεรƭ¯`•.,¸¸,.•´
******************************
snow1
🌨❄•°•.•°•ƭεรƭ•°•.•°•❄🌨️
******************************
snow2
✼　 ҉  ƭεรƭ   ҉ 　✼
******************************
soccer1
●●--●●◄⚽️► ƭεรƭ ◄⚽️►●●--●●
******************************
star1
【★】 ƭεรƭ 【★】
******************************
star10
-·=»★«=·-ƭεรƭ-·=»★«=·-
******************************
star11
【★】ƭεรƭ【★】
******************************
star12
★·.·¯`·.·★ƭεรƭ★·.·¯`·.·★
******************************
star13
(★)(¯`·.●.●ƭεรƭ●.●.·¯)(★)
******************************
star14
★★.·¯`★ƭεรƭ★¯`·.★★
******************************
star15
╰☆☆ƭεรƭ☆☆╮
******************************
star16
╚»★«╝ƭεรƭ╚»★«╝
******************************
star17
*̥̻̥̻̥͙*̻̥̻̥͙*̥̻̥͙*̻̥͙*̥͙ƭεรƭ*̥̻̥̻̥͙*̻̥̻̥͙*̥̻̥͙*̻̥͙*̥͙
******************************
star18
╰•★★ƭεรƭ★★•╯
******************************
star19
٭⊹°⨳°·..·°⨳°ƭεรƭ°⨳°·..·°⨳°⊹٭
******************************
star2
★·.·´¯`·.·★ ƭεรƭ ★·.·´¯`·.·★
******************************
star20
`✵•.¸,✵°✵.｡.✰ƭεรƭ✰.｡.✵°✵,¸.•✵´
******************************
star21
·.★·.·´¯`·.·★ƭεรƭ★·.·´¯`·.·★.·
******************************
star22
¨˜ˆ”°⍣~•✡⊹٭„¸ƭεรƭ¸„٭⊹✡•~⍣°”ˆ˜¨
******************************
star23
°•.•°¤*✬.•°°•ƭεรƭ°•°•.✬*¤°•.•°
******************************
star24
★¸.•☆•.¸★ƭεรƭ★⡀.•☆•.★
******************************
star25
✬☆*.•⨳•.¤⊹٭ƭεรƭ٭⊹¤.•⨳•.*☆✬
******************************
star3
╰☆⭐️ƭεรƭ⭐️☆╮
******************************
star4
★・・・・・・★ƭεรƭ★・・・・・・★
******************************
star5
【🌩】★【🌩】ƭεรƭ【🌩】★【🌩】
******************************
star6
☆━━━━━━☆ƭεรƭ☆━━━━━━☆
******************************
star7
・‥…━━━━━━━☆ƭεรƭ☆━━━━━━━…‥・
******************************
star8
【☆】★【☆】ƭεรƭ【☆】★【☆】
******************************
star9
☆❋──❁ƭεรƭ❃──❋
******************************
temple1
エｴｪｪｪƭεรƭｪｪｪｴエ
******************************
title1
»»————-ƭεรƭ————-««
******************************
tree1
🎄✖🎄✖🎄⇉ ƭεรƭ⇇🎄✖🎄✖🎄
******************************
wall1
├┬┴┬┴ƭεรƭ┬┴┬┴┤
******************************
wave1
▁ ▂ ▄ ▅ ▆ ▇ █ƭεรƭ█ ▇ ▆ ▅ ▄ ▂ ▁
******************************
wave2
▉▇▆▅▄▃▂▂▂_ƭεรƭ_▂▂▃▄▅▆▇▉▉
******************************
wave3
▂▃▅▇█▓▒░ƭεรƭ░▒▓█▇▅▃▂
******************************
wave4
▂▃▄▅▆▇▉▉ƭεรƭ▉▉▇▆▅▄▃▂
******************************
wave5
︵‿︵‿︵‿︵‿︵ ƭεรƭ‿︵‿︵‿︵‿︵‿︵‿︵
******************************
wave6
░▒▓█ƭεรƭ█▓▒░
******************************
wave7
(¯`·._(¯`·._(¯`·._ƭεรƭ_.·¯)_.·¯)_.·¯)
******************************
wave8
_/¯/__/¯/_ƭεรƭ_/¯/__/¯/_
******************************
wave9
/)/)/)/)/)/)ƭεรƭ/)/)/)/)/)/)
******************************
>>> tprint("    test   ",font="fancy42",decoration="barcode1")
▌│█║▌║▌║     ţ€$ţ    ║▌║▌║█│▌
>>> decor("barcode1") + text2art("    test   ",font="fancy42") + decor("barcode1",True)
'▌│█║▌║▌║     ţ€$ţ    ║▌║▌║█│▌'
>>> decor("barcode1")
'▌│█║▌║▌║ '
>>> decor("barcode1",True)
' ║▌║▌║█│▌'
>>> decor("barcode1",both=True)
['▌│█║▌║▌║ ', ' ║▌║▌║█│▌']
>>> decor(None)
Traceback (most recent call last):
        ...
art.art.artError: The 'decoration' type must be str.
>>> random.seed(24)
>>> decor1 = decor("random")
>>> random.seed(45)
>>> decor2 = decor("random")
>>> decor1 == decor2
False
>>> random.seed(24)
>>> Art = text2art("test","rnd-na")
>>> random.seed(45)
>>> Art2 = text2art("test","rnd-na")
>>> Art == Art2
False
>>> random.seed(24)
>>> Art = text2art("test","mix")
>>> random.seed(45)
>>> Art2 = text2art("test","mix")
>>> Art == Art2
False
>>> tprint("test","fancy1")
тεsт
<BLANKLINE>
>>> aprint("UnicodeEncodeError")
[Warning] 'UnicodeEncodeError' art is not printable in this environment.
>>> tprint("test","UnicodeEncodeError")
[Warning] 'UnicodeEncodeError' font is not printable in this environment.
>>> tprint("test","UnicodeEncodeError",decoration="angry1")
[Warning] 'UnicodeEncodeError' font or 'angry1' decoration is not printable in this environment.
>>> Data = tsave("test@34",font="antrophobia",filename="antrophobia.txt")
Saved!
Filename: antrophobia.txt
>>> Data = tsave("test@34",font="fancy37",filename="fancy37.txt")
Saved!
Filename: fancy37.txt
>>> Data = tsave("test",font="fancy6",filename="fancy6dec.txt",decoration="chess1")
Saved!
Filename: fancy6dec.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> Data = tsave("test",font="fancy6",filename="fancy6dec.txt",overwrite=True,decoration="bar1")
Saved!
Filename: fancy6dec.txt
>>> Data["Message"]
'OK'
>>> Data["Status"]
True
>>> FONT_COUNTER == (len(ASCII_FONTS) + len(NON_ASCII_FONTS))
True
>>> ART_COUNTER == (len(ASCII_ARTS) + len(NON_ASCII_ARTS))
True
>>> os.remove("antrophobia.txt")
>>> os.remove("fancy37.txt")
>>> os.remove("fancy6dec.txt")

'''
